import json
import shutil
import sys
import tempfile
import traceback

# Import our color utility functions
from color_utils import assign_color
from color_manager import color_manager
from typing import Dict, List
import pandas as pd
from PySide6.QtCore import Qt, QSize, QTime, QPoint, QTimer, QEasingCurve
from PySide6.QtGui import QColor, QPixmap, QIcon, QAction, QKeySequence
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QMainWindow, QMessageBox, QFileDialog, QVBoxLayout, \
    QTableWidgetItem, QHBoxLayout, QPushButton, QDialog, QLabel, QScrollArea, QTabWidget, QInputDialog, QSizePolicy, \
    QToolBar
from matplotlib import pyplot as plt
from matplotlib.patches import Rectangle
from gui import Ui_VaprIdexMainWindow, MultiStateToggleSwitch, CustomSplashScreen
from gui.dialog import DataVisualizationWidget, ReportPreviewDialog, CustomNavigationToolbar, \
    TemperatureDataConfigDialog, ExistingDataLoadDialog, PhotoPreviewDialog, Ui_Form
from src.database import DatabaseHandler, DatabaseConfig
from src.analysis import TemperatureAnalyzer, PressureAnalyzer, Performance
from src.database.database_login import DatabaseLoginDialog
from src.report_generation import TestReportGenerator, ImageHandler
from src.report_generation.setup_fonts import setup_fonts
from src.visualization import PlotManager
from src.data import DataLoader
from gui.widgets import TemperatureSelectionWidget, PlotCanvas
from src.utils import get_resource_path, safe_float, InitThread, HoverButton
from user_authentication import UserAuth, VerificationDialog
from src.animation import SectionAnimation
from color_manager import color_manager
import os

from user_authentication.auth import LoginDialog, UserManagementDialog, PasswordResetDialog
from data_recovery import AutoSaver


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Set up main UI components
        self._initialize_ui()

        # Initializing the Existing test data Dialog FIRST
        self.existing_data_dialog = ExistingDataLoadDialog()

        # Handle Section Animation
        self._initialize_section_animation()

        # Initialize core components for Data loading and Plotting
        self._initialize_core_components()

        # Set up data structures for data storage and state tracking
        self._initialize_data_structures()

        # Setting up connection for the section names in the left panel and the associated sub-section
        self._initialize_section_connections()

        self.ui.lblCurentSection.setText('Load Data')

        # Defining photo widgets mapping for small preview
        self._initialize_photo_widgets_mapping()

        # Connecting photo selection buttons
        self.setup_photo_connections()

        # Setting up all the icons from the resources folder
        self.setup_icons()

        # Setting up scroll area for the plot preview in plots-included window
        self._initialize_plot_preview()

        # Setting up the plot initialization
        self._initialize_plot()

        # Database initialization for Data Visualization
        self._initialize_Database()

        # Initializing Auto Data saver
        self.auto_saver = AutoSaver(self)

        # Add a timer to force enable the plots button after the application starts
        QTimer.singleShot(2000, self.force_enable_plots_button)

        # Initializing required properties or connections
        self.setup_connections()

        # Setting the Minimum value of field - Propellant RI after test
        self.ui.subLnEdtPropRIBefFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRIAftFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRI.setSpecialValueText("NA")

        # Blocking the plots section initially
        self.ui.btnPlots.setEnabled(False)

    def _initialize_Database(self):
        """Initialize database-related UI elements without connecting to database."""
        self.db_handler = None

        # Configure the propellant concentration spinbox
        self.ui.propellant_ri.setDecimals(2)
        self.ui.propellant_ri.setRange(0, 100)
        self.ui.propellant_ri.setSingleStep(0.01)
        self.ui.propellant_ri.setKeyboardTracking(False)

        # Set up search timer
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        # Configure spinboxes
        self.setupSpinBoxes()

        # Connect signals
        self.connectSignals()

    def initialize_database_connection(self):
        """Handle database connection when the database button is clicked."""
        # Show database login dialog
        login_dialog = DatabaseLoginDialog(self)
        if login_dialog.exec() == QDialog.Accepted:
            try:
                # Set the password in configuration
                DatabaseConfig.set_database_password(login_dialog.password)

                # Initialize database handler
                self.db_handler = DatabaseHandler()

                # Test connection
                test_params = DatabaseConfig.get_connection_params(is_server=True)
                success, message = DatabaseConfig.test_connection(test_params)

                if success:
                    # Show database-related UI elements
                    self.mode_toggle.update_modes(["New Test", "Existing Test", "Database"])
                    # Load initial data
                    self.load_all_records()

                    # Hide the database button
                    self.database_button_action.setVisible(False)

                    QMessageBox.information(self, "Success", "Connected to database successfully!")
                else:
                    QMessageBox.critical(self, "Connection Error", f"Failed to connect to database: {message}")
                    self.db_handler = None
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to initialize database: {str(e)}")
                self.db_handler = None

    def _initialize_plot(self):
        # Add temperature selection widget to left panel
        self.temp_selection_widget = TemperatureSelectionWidget()
        self.ui.tempMatrixDataSelection.layout().addWidget(self.temp_selection_widget)

        # Connect selection change signal
        self.temp_selection_widget.dataSelectionChanged.connect(
            self.update_temperature_plot
        )

        # Connect order change signal
        self.temp_selection_widget.orderChanged.connect(
            self.handle_temperature_order_changed
        )

        # Linking the back to Plot button in the temperature matrix window
        self.ui.btnBckToPlot.clicked.connect(lambda: self.ui.contentStack.setCurrentWidget(self.ui.plots))

        # Use centralized color manager's color dictionary for random color assignment
        self.color = color_manager.color  # Direct reference to centralized color dictionary

        # Add a method to get color for a column using centralized manager
        self.get_column_color = lambda column: color_manager.get_color(column)

        self.ui.pressurePlotBtnFrame.hide()

        # Initializing tab widget for pressure plot
        self.tab_widget = None
        self.current_tab_name = ""

        # Linking the Plot title change to plot refresh
        self.ui.lnEditPlotTitle.editingFinished.connect(self.plot_with_title)

        # Initialize figure and axes
        self.figure = None
        self.axes = None
        self.canvas = None

        # Initialize figure and axes for selected range pressure plot
        self.figure_selected = None
        self.axes_selected = None
        self.canvas_selected = None
        self.axhline = None

    def _initialize_plot_preview(self):
        # Setting up scroll area for plot previews
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(10)
        scroll_layout.setContentsMargins(10, 10, 10, 10)
        scroll_layout.setAlignment(Qt.AlignTop)
        self.ui.scrollAreaWidgetContents.setLayout(scroll_layout)

        # Setting minimum width for scroll area content to prevent horizontal scrolling
        self.ui.scrollAreaWidgetContents.setMinimumWidth(200)

        # Enabling vertical scrolling
        self.ui.scrlAreaReportPreview.setWidgetResizable(True)
        self.ui.scrlAreaReportPreview.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.ui.scrlAreaReportPreview.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

    def _initialize_photo_widgets_mapping(self):
        self.photo_widgets = {
            'prop_before': {
                'widget': self.ui.widgetPropPhotoBef_2,
                'line_edit': self.ui.subLnEdtPropPhtoBfr_2,
                'button': self.ui.btnPropPhotoBef_2,
                'preview': self.ui.lblPropPhotoPrevBef_2,
                'label': "Photo of Propellant (before test)"
            },
            'prop_after': {
                'widget': self.ui.widgetPropPhotoAft_2,
                'line_edit': self.ui.subLnEdtPropPhtoAft_2,
                'button': self.ui.btnPropPhotoAft_2,
                'preview': self.ui.lblPropPhotoPrevAft_2,
                'label': "Photo of Propellant (after test)"
            },
            'cat_before': {
                'widget': self.ui.widgetCatPhotoBef_2,
                'line_edit': self.ui.subLnEdtCatPhtoBfr_2,
                'button': self.ui.btnCatPhotoBef_2,
                'preview': self.ui.lblCatPhotoPrevBef_2,
                'label': "Photo of Catalyst (before test)"
            },
            'cat_after': {
                'widget': self.ui.widgetCatPhotoAft_2,
                'line_edit': self.ui.subLnEdtCatPhtoAft_2,
                'button': self.ui.btnCatPhotoAft_2,
                'preview': self.ui.lblCatPhotoPrevAft_2,
                'label': "Photo of Catalyst (after test)"
            }
        }

    def _initialize_section_connections(self):
        # Mapping for contentStack pages to left panel buttons
        self.page_button_mapping = {
            'basicInformation': (self.ui.btnTestPrereq, self.ui.btnBasicInfo),
            'systemSpecification': (self.ui.btnTestPrereq, self.ui.btnSysSpec),
            'propellantSpecification': (self.ui.btnTestPrereq, self.ui.btnPropSpec),
            'catalystSpecification': (self.ui.btnTestPrereq, self.ui.btnCatSpec),
            'componentDetails': (self.ui.btnTestPrereq, self.ui.btnCompDet),
            'testDetails': (self.ui.btnTestPrereq, self.ui.btnTestDet),
            'heaterInformation': (self.ui.btnHtrOp, self.ui.btnHtrInfo),
            'heaterCycles': (self.ui.btnHtrOp, self.ui.btnHtrCyc),
            'postTestingObs': (self.ui.btnPstTestAn, self.ui.btnPstTestObs),
            'catalystPostAna': (self.ui.btnPstTestAn, self.ui.btnCatPostAn),
            'propellantPostAn': (self.ui.btnPstTestAn, self.ui.btnPropPostAn),
            'temperaturematrix': (self.ui.btnPerformance, self.ui.btnTempMatrix),
            'performance': (self.ui.btnPerformance, None),
            'testAuthorization': (self.ui.btnTestAuthorization, None)
        }

        # Mapping for section headers
        self.section_headers = {
            'basicInformation': "Test Prerequisite",
            'systemSpecification': "Test Prerequisite",
            'propellantSpecification': "Test Prerequisite",
            'catalystSpecification': "Test Prerequisite",
            'componentDetails': "Test Prerequisite",
            'testDetails': "Test Prerequisite",
            'heaterInformation': "Heater Operation",
            'heaterCycles': "Heater Operation",
            'note': "Heater Operation",
            'postTestingObs': "Post Test Analysis",
            'catalystPostAna': "Post Test Analysis",
            'propellantPostAn': "Post Test Analysis",
            'plots': "Plots",
            'performance': "Performance",
        }

        # Store the default button style
        self.default_button_style = """
                            QPushButton{
	background-color: #1e293c;
	border-radius: 17px;
	border: 1px solid #303030;
	padding:5px;
	font-size: 19px;
	font-family: Helvetica;
	font-weight: bold;
}

QPushButton:hover{
	background-color:#47a08e;
}

QPushButton:pressed{
	background-color:black;
}
                        """

        self.default_button_style_subsections = """
            QPushButton{
	background-color: #1e1e1e;
	color: white;
	padding:5px;
	font-size:17px;
	border:1px solid #446699;
	font-family: Arial;
}

QPushButton:hover{
	background-color:#47a08e;
}

QPushButton:pressed{
	background-color:#5C5C5C;
}
        """

        # Store the selected button style
        self.selected_button_style = """
                            QPushButton {
                                background-color: #B03781;
                                border-radius: 10px;
                                padding: 5px;
                                font-size: 17px;
                            }
                            """

    def _initialize_data_structures(self):
        """Initialize data storage and state tracking variables."""
        self.temperature_data = None
        self.pressure_data = None
        self.test_data = {}
        self.current_figure = None
        self.current_canvas = None
        self.current_toolbar = None
        self.plot_container = None
        self.current_plot_type = None
        self.filtered_temp_data = None
        self.selecting = False
        self.start_x = None
        self.rect = None
        self.current_rect = None
        self.selected_ranges = []
        self.selected_cols = []
        self.temp_dir = None

        # Initialize plot tracking
        self.report_plots = {
            'default': [],  # For default temperature plots
            'custom': []    # For custom plots
        }

        # Define a list of bright, visually distinct colors for plotting
        # This is the MASTER color palette that should be used everywhere
        self.color_palette = [
            '#01a5e3',  # Bright blue
            '#32c133',  # Bright green
            '#ee0100',  # Bright red
            '#f7d02e',  # Bright yellow
            '#2ca02c',  # Bright green variant
            '#d62728',  # Bright red variant
            '#9467bd',  # Bright purple
            '#1f77b4',  # Medium blue
            '#bcbd22',  # Olive green
            '#8c564b',  # Brown
            '#7f7f7f',  # Gray
            '#165152'   # Teal (darker but still visible)
        ]

        # Use centralized color manager's color dictionary for random color assignment
        self.color = color_manager.color  # Direct reference to centralized color dictionary

        # Store figures for report
        self.current_figures = {}

    def assign_color(self, column: str) -> str:
        """Assign color using centralized color manager"""
        return color_manager.get_color(column)

    def _initialize_core_components(self):
        """Initialize backend analysis and processing components."""
        self.data_loader = DataLoader(self)
        self.temp_analyzer = TemperatureAnalyzer()
        self.pressure_analyzer = PressureAnalyzer()
        self.plot_manager = PlotManager()
        self.image_handler = ImageHandler()

        # Setup fonts first
        if not setup_fonts():
            QMessageBox.critical(self, "Error",
                                 "Failed to setup required fonts. Please check your internet connection and try again.")
            sys.exit(1)

        # Then initialize report generator
        try:
            self.report_generator = TestReportGenerator()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to initialize report generator: {str(e)}")
            sys.exit(1)

    def _initialize_ui(self):
        """Initialize and setup the main UI components."""
        self.ui = Ui_VaprIdexMainWindow()
        self.ui.setupUi(self)

        # Setup plot layout
        self.plot_layout = QVBoxLayout()
        self.plot_layout.setContentsMargins(0, 10, 0, 0)
        self.ui.plotFrame.setLayout(self.plot_layout)

        # Adding database to menubar
        self.setup_menu()

        # Adding an app icon
        self.setWindowIcon(QIcon("assets/icon.ico"))

        # Removing the tab bar from the main tabWidget
        self.ui.tabWidget.tabBar().hide()

        # Removing the tab bar from the plot settings in the left panel
        self.ui.tabWidgetPlotSettings.tabBar().hide()

    def _initialize_authentication(self):
        """Handle user authentication and authorization."""
        self.auth = UserAuth()
        self.current_user = None

        if not self.show_login():
            return False



        # Update menu to include user management for admin
        if self.current_user and self.current_user.get("role") == "admin":
            self.setup_admin_menu()
        return True

    def _initialize_section_animation(self):
        # Find the scroll area
        self.left_scroll_area = None
        for child in self.ui.leftPanel.children():
            if isinstance(child, QScrollArea):
                self.left_scroll_area = child
                break

        if not self.left_scroll_area:
            print("Warning: Could not find scroll area in left panel")

        # Initializing animations with found scroll area
        self.section_animations = {
            'test_prereq': SectionAnimation(self.ui.testPrereqSubSections, self.left_scroll_area),
            'heater_op': SectionAnimation(self.ui.htrOpSubSections, self.left_scroll_area),
            'post_test': SectionAnimation(self.ui.pstTestAnSubSections, self.left_scroll_area),
            'plot_controls': SectionAnimation(self.ui.plotControlsFrame, self.left_scroll_area),
            'performance': SectionAnimation(self.ui.perforSubSections, self.left_scroll_area),
        }

        # Setup advanced animation settings
        self.setupAdvancedAnimation()

    def setup_menu(self):
        """Set up menu bar with database operations."""
        # Create toolbar for the toggle switch
        self.toolbar = QToolBar()
        self.toolbar.setMovable(False)
        self.toolbar.setFloatable(False)
        self.addToolBar(self.toolbar)

        # Add spacer to push toggle switch to center
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.toolbar.addWidget(spacer)

        # Add toggle switch with two modes only (remove Database mode)
        self.mode_toggle = MultiStateToggleSwitch(["New Test"])
        self.mode_toggle.modeChanged.connect(self.handle_mode_change)
        self.toolbar.addWidget(self.mode_toggle)

        # Add database button to toolbar
        self.database_button = HoverButton(self)
        self.database_button.setIcon(QIcon(get_resource_path("assets/database_icon.png")))
        self.database_button.setToolTip("Connect to Database")
        self.database_button.clicked.connect(self.initialize_database_connection)
        self.database_button.setStyleSheet("""
                            QPushButton {
                                background-color: transparent;
                                border: none;
                            }
                            QPushButton:hover {
                                background-color: rgba(255, 255, 255, 0.1);
                                border-radius: 3px;
                            }
                        """)
        self.database_button_action = self.toolbar.addWidget(self.database_button)

        # Add spacer after the toggle switch
        spacer2 = QWidget()
        spacer2.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.toolbar.addWidget(spacer2)

        # Style the toolbar
        self.toolbar.setStyleSheet("""
                QToolBar {
                    spacing: 0px;
                    border: none;
                    background-color: transparent;
                }
            """)

        # Setting up a tab switch slider for plot setting mode change in left panel
        self.mode_plot_settings = MultiStateToggleSwitch(['Temperature', 'Pressure'])
        self.mode_plot_settings.modeChanged.connect(self.handle_mode_change)
        self.mode_plot_settings.background_color = QColor(41, 47, 54)
        self.mode_plot_settings.handle_color = QColor(216, 30, 91)

        plot_settings_layout = QHBoxLayout(self.ui.plotSettingsTabSlider)
        plot_settings_layout.addWidget(self.mode_plot_settings)

        self.mode_plot_settings.modeChanged.connect(self.handle_plots_mode_change)

############################################################### Initialization End ################################################################
    def handle_temperature_order_changed(self, new_order: List[str]):
        """Handle temperature column reordering"""
        # Update your plots or data structures based on the new order
        self.update_temperature_plot()

    ############ User Authentication ##########################
    def show_login(self) -> bool:
        """Show login dialog and handle authentication"""
        login_dialog = LoginDialog(self.auth, self)
        if login_dialog.exec() == QDialog.Accepted and login_dialog.user_data:
            self.current_user = login_dialog.user_data
            # animation = AnimationDialog(self)
            # animation.play_animation(r"assets/Server Connection.gif")
            return True
        return False

    def setup_admin_menu(self):
        """Admin-specific menu items"""
        admin_menu = self.menuBar().addMenu('Admin')

        # User management action
        manage_users_action = QAction('Manage Users', self)
        manage_users_action.triggered.connect(self.show_user_management)
        admin_menu.addAction(manage_users_action)

        # Change password action
        change_password_action = QAction("Change Password", self)
        change_password_action.triggered.connect(self.show_admin_password_change)
        admin_menu.addAction(change_password_action)

    def show_admin_password_change(self):
        """Show Password reset dialog for admin"""
        if self.current_user and self.current_user.get("email"):
            dialog = PasswordResetDialog(self.auth, self)
            dialog.email_edit.setText(self.current_user["email"])
            dialog.email_edit.setEnabled(False) # Lock email field for admin
            dialog.exec()

    def show_user_management(self):
        """Show user management dialog"""
        if self.current_user["role"] == "admin":
            dialog = UserManagementDialog(self.auth, self)
            if dialog.exec() == QDialog.Accepted:
                # Show verification dialog after adding new user
                self.show_verification_dialog()

    def show_verification_dialog(self):
        """Show dialog for verifying user email"""
        dialog = VerificationDialog(self.auth, self)
        dialog.exec()

    def logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(
            self,
            'Logout',
            'Are you sure you want to logout?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.current_user = None
            self.close()
            # Restart application
            QApplication.instance().exit()

    #####################################################################

    def setupSpinBoxes(self):
        # Configure propellant concentration spinbox
        self.ui.propellant_ri.setDecimals(2)
        self.ui.propellant_ri.setRange(0, 100)
        self.ui.propellant_ri.setSingleStep(0.01)
        self.ui.propellant_ri.setKeyboardTracking(False)

        # Configure tank temperature spinbox
        self.ui.tank_temp.setDecimals(2)
        self.ui.tank_temp.setRange(0, 1000)
        self.ui.tank_temp.setSingleStep(0.1)
        self.ui.tank_temp.setKeyboardTracking(False)

    def connectSignals(self):
        # Setup search timer
        self.search_timer = QTimer(self)
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        # Connect input changes to delayed search
        self.ui.test_no.textChanged.connect(lambda: self.search_timer.start(300))
        self.ui.catalyst_name.textChanged.connect(lambda: self.search_timer.start(300))
        self.ui.propellant_ri.valueChanged.connect(lambda: self.search_timer.start(300))
        self.ui.tank_temp.valueChanged.connect(lambda: self.search_timer.start(300))


        self.ui.results_table_2.doubleClicked.connect(self.load_selected_test)

    def trigger_delayed_search(self):
        """Trigger search after a short delay to prevent rapid consecutive searches"""
        self.search_timer.start(300)  # 300ms delay

    def load_all_records(self):
        """Load all records into the table"""
        try:
            results = self.db_handler.filter_tests({})  # Empty params to get all records
            self.update_table_2(results)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading records: {str(e)}")

    def perform_search(self):
        try:
            params = {}

            test_no = self.ui.test_no.text().strip()

            if test_no:
                params['testNo'] = test_no

            catalyst_name = self.ui.catalyst_name.text().strip()
            if catalyst_name:
                params['catalystName'] = catalyst_name

            prop_conc = self.ui.propellant_ri.value()
            if prop_conc > 0:
                params['propellantConc'] = prop_conc

            tank_temp = self.ui.tank_temp.value()
            if tank_temp > 0:
                params['tankTemp'] = tank_temp

            results = self.db_handler.filter_tests(params)
            if results:
                self.update_table_2(results)
            else:
                self.ui.results_table_2.setRowCount(0)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error in search: {str(e)}")

    def update_table_2(self, results):
        try:
            self.ui.results_table_2.setRowCount(len(results))

            for i, result in enumerate(results):
                # Format the numerical values
                prop_conc = f"{result['propellant_conc']:.2f}" if result['propellant_conc'] else ""

                self.ui.results_table_2.setItem(i, 0, QTableWidgetItem(str(result['test_id'])))
                self.ui.results_table_2.setItem(i, 1, QTableWidgetItem(str(result['test_no'])))
                self.ui.results_table_2.setItem(i, 2, QTableWidgetItem(str(result['test_date'])))
                self.ui.results_table_2.setItem(i, 3, QTableWidgetItem(str(result['catalyst_name'])))
                self.ui.results_table_2.setItem(i, 4, QTableWidgetItem(prop_conc))

            self.ui.results_table_2.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error updating table: {str(e)}")
            traceback.print_exc()

    def load_selected_test(self):
        """Load the selected test data into the databaseViewer frame."""
        # Check if a row is selected
        selected_rows = self.ui.results_table_2.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select a test to load.")
            return

        # Get the test number from the selected row (column 1)
        try:
            test_no = self.ui.results_table_2.item(selected_rows[0].row(), 1).text()
        except (IndexError, AttributeError) as e:
            QMessageBox.warning(self, "Error", "Failed to retrieve test number.")
            return

        # Check if databaseViewer already has a layout, reuse it or create a new one
        if self.ui.databaseViewer.layout() is None:
            databaseViewer_layout = QVBoxLayout(self.ui.databaseViewer)
        else:
            databaseViewer_layout = self.ui.databaseViewer.layout()

        # Clear existing widgets in the layout
        while databaseViewer_layout.count():
            item = databaseViewer_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()  # Properly delete the old widget

        # Create and add the new DataVisualizationWidget
        data_visualization = DataVisualizationWidget(test_no, self.ui.databaseViewer)
        databaseViewer_layout.addWidget(data_visualization)


############################ Filter Window ########################################

    def plot_with_title(self):
        current_plot = self.ui.lblCurentSection.text()

        if current_plot == 'Pressure Plot':
            if self.tab_widget.count() > 1:
                if self.tab_widget.currentIndex() == 0:
                    self.axes_selected.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
                    self.canvas_selected.draw()
                elif self.tab_widget.currentIndex() == 1:
                    self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
                    self.canvas.draw()
            else:
                self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                          color='#09090b',
                          fontsize=12,
                          fontweight='bold')
                self.canvas.draw()
        else:
            self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
            self.canvas.draw()

    def setup_icons(self):
        """Setup icons for buttons"""
        try:

            # Set application icon
            app_icon = QIcon()
            app_icon_path = get_resource_path("assets/icon.ico")
            app_icon.addFile(app_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.setWindowIcon(app_icon)
            QApplication.instance().setWindowIcon(app_icon)

            # Set Windows taskbar icon explicitly
            if hasattr(sys, 'frozen'):
                import ctypes
                myappid = 'manastuspace.vapridexanalyzer.1.0'
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)

            # Back button icon for Heater cycles
            back_icon = QIcon()
            back_icon_path = get_resource_path("assets/left_arrow.drawio.png")
            back_icon.addFile(back_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.btnCycleBack.setIcon(back_icon)
            self.ui.btnCycleBack.setIconSize(QSize(70, 70))

            # Next button icon for Heater cycles
            next_icon = QIcon()
            next_icon_path = get_resource_path("assets/right_arrow.drawio.png")
            next_icon.addFile(next_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.btnCycleNext.setIcon(next_icon)
            self.ui.btnCycleNext.setIconSize(QSize(70, 70))

            # Temperature load Icon
            temperature_icon = QIcon()
            temperature_icon_path = get_resource_path("assets/temperature_icon.png")
            temperature_icon.addFile(temperature_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.temp_icon.setIcon(temperature_icon)
            self.ui.temp_icon.setIconSize(QSize(185, 185))
            self.ui.btnTempDataInd.setIcon(temperature_icon)
            self.ui.btnTempDataInd.setIconSize(QSize(45, 45))

            # Pressure load Icon
            pressure_icon = QIcon()
            pressure_icon_path = get_resource_path("assets/pressure_icon.png")
            pressure_icon.addFile(pressure_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.pressure_icon.setIcon(pressure_icon)
            self.ui.pressure_icon.setIconSize(QSize(185, 185))
            self.ui.btnPressureDataInd.setIcon(pressure_icon)
            self.ui.btnPressureDataInd.setIconSize(QSize(55, 55))

            # Setting down arrow icon for all combo boxes
            down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
            down_arrow_style = """
                            QComboBox::down-arrow {
                                image: url("%s");
                                width: 24px;
                                height: 24px;
                            }
                            QComboBox::drop-down {
                                border: none;
                                background: transparent;
                                width: 20px;
                                margin-right: 8px;
                            }
                        """ % down_arrow_path

            # Applying the style to all combo boxes
            combo_boxes = [
                self.ui.comboBoxXAxisTemp,
                self.ui.comboBoxYAxisTemp,
                self.ui.comboBoxXAxisPressure,
                self.ui.comboBoxYAxisPressure
            ]

            for combo_box in combo_boxes:
                current_style = combo_box.styleSheet()
                combo_box.setStyleSheet(current_style + down_arrow_style)


        except Exception as e:
            print(f"Error setting up icons: {str(e)}")

    def reset_input_fields(self):
        # Reset temp_data.json file to a blank JSON file
        user_home = os.path.expanduser("~")
        json_path = os.path.join(user_home, "temp_data.json")

        try:
            if os.path.exists(json_path):
                with open(json_path, "w") as temp_json_file:
                    json.dump({}, temp_json_file)
                print(f"{json_path} has been emptied.")

                # Also reset temperature_data and pressure_data to prevent auto-save from creating fallback data
                self.temperature_data = None
                self.pressure_data = None
                self.filtered_temp_data = None
                self.report_plots = {'default': [], 'custom': []}

                # Disable the plots button
                self.ui.btnPlots.setEnabled(False)
                self.ui.plotControlsFrame.hide()

                # Reset the temperature and pressure data indicators
                self.ui.btnTempDataInd.setStyleSheet("")
                self.ui.btnTempDataLoad.setStyleSheet("")
                self.ui.btnPressureDataInd.setStyleSheet("")
                self.ui.btnPressureDataLoad.setStyleSheet("")
                self.ui.btnTempMatrix.setEnabled(False)
            else:
                print(f"{json_path} does not exists in the current directory.")

        except Exception as e:
            print(f"Error resetting temp_data.json: {str(e)}")

        self.ui.lblFiringDuration.clear()
        self.ui.subLnEdtAim.clear()
        self.ui.subLnEdtProp.clear()
        self.ui.subLnEdtPropRI.clear()
        self.ui.subLnEdtCat.clear()
        self.ui.subLnEdtTestNo.clear()
        self.ui.subLnEdtTestDate.clear()
        self.ui.subLnEdtChmbrNo.clear()
        self.ui.subLnEdtChmbrMat.clear()
        self.ui.subLnEdtChmbrDept.clear()
        self.ui.subLnEdtInternalChmbrDia.clear()
        self.ui.subLnEdtExternalChmbrDia.clear()
        self.ui.subLnEdtNozlThrtDime.clear()
        self.ui.subLnEdtRetainerPltOrfcDia.clear()
        self.ui.subLnEdtMeshMat.clear()
        self.ui.subLnEdtMeshSize.clear()
        self.ui.subLnEdtTypeOfProp.clear()
        self.ui.subLnEdtConcBefTest.clear()
        self.ui.subLnEdtStability.clear()
        self.ui.subLnEdtWghtOfPropBefTest.clear()
        self.ui.subLnEdtWghtOfPropAftTest.clear()
        self.ui.subLnEdtCatType.clear()
        self.ui.subLnEdtCatGrade.clear()
        self.ui.subLnEdtCatSize.clear()
        self.ui.subLnEdtCatWghtBefTest.clear()
        # self.ui.subLnEdtPrehtTemp.clear()
        # self.ui.subLnEdtPressSensType.setText("Piezo resistive type vacuum pressure transmitter")
        # self.ui.subLnEdtPressSensRange.setText("0~1 bar")
        # self.ui.subLnEdtPressSensIO.setText("\u00b115 VDC, 4-20 mA (0-5 V)")

        self.ui.Vac_Chamb_Pressure_Sensr_type_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_range_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.clear()
        self.ui.Prop_Tank_Pressure_Sensr_type_Input.clear()
        self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Prop_Tank_Pressure_Snsr_range_Input.clear()
        self.ui.Prop_Tank__Pressure_Snsr_IO_Input.clear()
        self.ui.Thruster_Pressure_Sensr_type_Input.clear()
        self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Thruster_Pressure_Snsr_range_Input.clear()
        self.ui.Thruster_Pressure_Snsr_IO_Input.clear()
        self.ui.subLnEdtHtrType_2.setText("Silicon Patch heater")
        self.ui.subLnEdtHtrInpPower.setValue(35)
        self.ui.subLnEdtPropTnkHtrCtOfTemp.clear()
        self.ui.subLnEdtPropTnkHtrRstTemp.clear()
        self.ui.subLblTestProcValue.setPlainText(
            'Switch on the heaters on the propellant tank. Let the heater be turned on till the cut-off '
            'temperature of the tank bottom is reached. Switch-off the heater when the cut-off temperature '
            'is reached. Restart the heater when the temperature falls below the reset temperature '
            '(2°C below the cut-off temperature). Repeat this process for 4 cycles. Each cycle is defined as '
            'a tank reaching its cut-off temperature and then re-heating.')
        self.ui.subLnEdtHtrType.clear()
        self.ui.subLnEdtHtrInpVoltage.clear()
        self.ui.subLnEdtHtrInpCurrent.clear()
        self.ui.subLnEdtHtrInpWattage.clear()
        self.ui.subLnEdtHtrCtOfTemp.clear()
        self.ui.subLnEdtHtrRstTemp.clear()

        time = QTime(00, 00)
        self.ui.cyc1SubLnEdtHtrSwtOnTime.setTime(time)
        self.ui.cyc1SubLnEdtHtrSwtONCorspgTankPressure.clear()
        self.ui.cyc1SubLnEdtHtrSwtONCorspgThrusterPressure.clear()
        self.ui.cyc1SubLnEdtHtrSwtOffTime.setTime(time)
        self.ui.cyc1SubLnEdtHtrSwtOFFCorspgTankPressure.clear()
        self.ui.cyc1SubLnEdtHtrSwtOFFCorspgThrusterPressure.clear()
        self.ui.cyc1SubLnEdtMaxTemp.clear()
        self.ui.cyc1SubLnEdtLoc.clear()
        self.ui.cyc1SubLnEdtCorrespgTemp.clear()

        self.ui.cyc2SubLnEdtHtrSwtOnTime.setTime(time)
        self.ui.cyc2SubLnEdtHtrSwtONCorspgTankPressure.clear()
        self.ui.cyc2SubLnEdtHtrSwtONCorspgThrusterPressure.clear()
        self.ui.cyc2SubLnEdtHtrSwtOffTime.setTime(time)
        self.ui.cyc2SubLnEdtHtrSwtOFFCorspgTankPressure.clear()
        self.ui.cyc2SubLnEdtHtrSwtOFFCorspgThrusterPressure.clear()
        self.ui.cyc2SubLnEdtMaxTemp.clear()
        self.ui.cyc2SubLnEdtLoc.clear()
        self.ui.cyc2SubLnEdtCorrespgTemp.clear()

        self.ui.cyc3SubLnEdtHtrSwtOnTime.setTime(time)
        self.ui.cyc3SubLnEdtHtrSwtONCorspgTankPressure.clear()
        self.ui.cyc3SubLnEdtHtrSwtONCorspgThrusterPressure.clear()
        self.ui.cyc3SubLnEdtHtrSwtOffTime.setTime(time)
        self.ui.cyc3SubLnEdtHtrSwtOFFCorspgTankPressure.clear()
        self.ui.cyc3SubLnEdtHtrSwtOFFCorspgThrusterPressure.clear()
        self.ui.cyc3SubLnEdtMaxTemp.clear()
        self.ui.cyc3SubLnEdtLoc.clear()
        self.ui.cyc3SubLnEdtCorrespgTemp.clear()

        self.ui.cyc4SubLnEdtHtrSwtOnTime.setTime(time)
        self.ui.cyc4SubLnEdtHtrSwtONCorspgTankPressure.clear()
        self.ui.cyc4SubLnEdtHtrSwtONCorspgThrusterPressure.clear()
        self.ui.cyc4SubLnEdtHtrSwtOffTime.setTime(time)
        self.ui.cyc4SubLnEdtHtrSwtOFFCorspgTankPressure.clear()
        self.ui.cyc4SubLnEdtHtrSwtOFFCorspgThrusterPressure.clear()
        self.ui.cyc4SubLnEdtMaxTemp.clear()
        self.ui.cyc4SubLnEdtLoc.clear()
        self.ui.cyc4SubLnEdtCorrespgTemp.clear()

        self.ui.subLnEdtChmbrNoPostTestObs.clear()
        self.ui.subLnEdtChmbrLen_2.clear()
        self.ui.subLnEdtChmbrIntDia_2.clear()
        self.ui.subLnEdtChmbrExtDia_2.clear()
        self.ui.subLnEdtMeshCond_2.clear()
        self.ui.subLnEdtRetainerPltCond_2.clear()
        self.ui.subLnEdtCatPhtoBfr_2.setText('No Photo Selected')
        self.ui.subLnEdtCatPhtoAft_2.setText('No Photo Selected')
        self.ui.subLnEdtPropPhtoBfr_2.setText('No Photo Selected')
        self.ui.subLnEdtPropPhtoAft_2.setText('No Photo Selected')
        self.ui.lblCatPhotoPrevBef_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblCatPhotoPrevAft_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblPropPhotoPrevBef_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblPropPhotoPrevAft_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblCatPhotoPrevBef_2.clear()
        self.ui.lblCatPhotoPrevAft_2.clear()
        self.ui.lblPropPhotoPrevBef_2.clear()
        self.ui.lblPropPhotoPrevAft_2.clear()
        self.ui.subLnEdtNote.clear()

        self.ui.subLnEdtCatDet.clear()
        self.ui.subLnEdtCatColBfr.clear()
        self.ui.subLnEdtCatColAft.clear()
        self.ui.subLnEdtCatWghtFild.clear()
        self.ui.subLnEdtCatWghtRecvrd.clear()
        self.ui.subLnEdtCatLosPerc.clear()

        self.ui.subLnEdtPropDet_2.clear()
        self.ui.subLnEdtPropColBef_2.clear()
        self.ui.subLnEdtPropColAft_2.clear()
        self.ui.subLnEdtPropWghtFild_2.clear()
        self.ui.subLnEdtPropWghtRecvrd_2.clear()
        self.ui.subLnEdtPropUsedPerc_2.clear()
        self.ui.subLnEdtPropRIBefFirg_2.clear()
        self.ui.subLnEdtPropRIAftFirg_2.clear()
        self.ui.subLnEdtFirgDur_2.clear()
        self.ui.subLnEdtApproxMassFlowRate_2.clear()

        self.ui.subLblPropBefRITable.clear()
        self.ui.subLblPropAftRITable.clear()
        self.ui.subLblPropBefConcTable.clear()
        self.ui.subLblPropAftConcTable.clear()

        self.ui.tableTemperatureAnalysis.clear()

        self.ui.subLnEdtChambPressure.clear()
        self.ui.subLnEdtVacPressure.clear()
        self.ui.subLnEdtChambTemp.clear()
        self.ui.subLnEdtCharVelo.clear()
        self.ui.subLnEdtCoefOfThrust.clear()
        self.ui.subLnEdtBurnTime.clear()
        self.ui.subLnEdtMassFlowRate.clear()
        self.ui.subLnEdtThrust.clear()
        self.ui.subLnEdtSpcImpulse.clear()
        self.ui.subLnEdtTotImpulse.clear()

        self.ui.lblAim.hide()
        self.ui.lblPropellant.hide()
        self.ui.lblCatalyst.hide()
        self.ui.testNoFrame.hide()

        # Resetting indicator color orange
        self.ui.tempMatrixIndicator.setStyleSheet("""
                        background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 0, 0, 255), stop:1 rgba(255, 67, 0, 255));
                        border-radius: 7px;
                        """
                                                  )

        self.ui.lnEdtInitialPropMass.setValue(0)
        self.ui.lnEdtFinalPropMass.setValue(0)
        self.ui.lnEdtChambPressRangeMin.setValue(0)
        self.ui.lnEdtChambPressRangeMax.setValue(0)
        self.ui.lnEdtVacPressRangeMin.setValue(0)
        self.ui.lnEdtVacPressRangeMax.setValue(0)

        self.temp_selection_widget.clear_all_columns()
        self.ui.tempMatrixDataSelection.hide()

        self.ui.comboBoxXAxisTemp.clear()
        self.ui.comboBoxYAxisTemp.clear()
        self.ui.lnEdtXLabelTemp.clear()
        self.ui.lnEdtYLabelTemp.clear()
        self.ui.comboBoxXAxisPressure.clear()
        self.ui.comboBoxYAxisPressure.clear()
        self.ui.lnEdtXLabelPressure.clear()
        self.ui.lnEdtYLabelPressure.clear()
        self.ui.lnEdtRangeMinPressure.setValue(0)
        self.ui.lnEdtRangeMaxPressure.setValue(0)

        self.ui.lnEdtY0PressureRelation.clear()
        self.ui.lnEdtY1PressureRelation.clear()
        self.ui.lnEdtY2PressureRelation.clear()

        self.temperature_data = None
        self.pressure_data = None
        self.test_data = {}

    def setup_photo_connections(self):
        """Connect photo selection buttons to their handlers"""
        try:
            for photo_id, widgets in self.photo_widgets.items():
                # Use lambda with default argument to prevent late binding issues
                widgets['button'].clicked.connect(
                    lambda checked, pid=photo_id: self.select_photo(pid)
                )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up photo connections: {str(e)}")

    def setup_connections(self):
        """Set up all necessary connections for the application"""
        ## Connecting basic info inputs
        self.ui.subLnEdtTestNo.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtTestNo, 'test_no'))
        self.ui.subLnEdtAim.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtAim, 'aim'))
        self.ui.subLnEdtProp.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtProp, 'propellant'))
        self.ui.subLnEdtCat.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtCat, 'catalyst'))
        self.ui.subLnEdtPropRI.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtPropRI, 'prop_RI'))

        ## For Propellant Parameters
        self.ui.lnEdtInitialPropMass.editingFinished.connect(
            lambda: self.value_changed(self.ui.lnEdtInitialPropMass, 'prop_initial'))
        self.ui.subLnEdtWghtOfPropBefTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtWghtOfPropBefTest, 'prop_initial'))
        self.ui.subLnEdtPropWghtFild_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropWghtFild_2, 'prop_initial'))
        self.ui.lnEdtFinalPropMass.editingFinished.connect(
            lambda: self.value_changed(self.ui.lnEdtFinalPropMass, 'prop_final'))
        self.ui.subLnEdtWghtOfPropAftTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtWghtOfPropAftTest, 'prop_final'))
        self.ui.subLnEdtPropWghtRecvrd_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropWghtRecvrd_2, 'prop_final'))

        ## For Catalyst Parameters
        self.ui.subLnEdtCatWghtBefTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtBefTest, 'cat_initial'))
        self.ui.subLnEdtCatWghtFild.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtFild, 'cat_initial'))
        self.ui.subLnEdtCatWghtRecvrd.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtRecvrd, 'cat_final'))

        ## Mass flow rate
        self.ui.subLnEdtPropRIAftFirg_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropRIAftFirg_2, 'mass_flow_rate'))

        # Connecting data loading buttons
        self.ui.btnTempDataLoad.clicked.connect(self.load_temperature_data)
        self.ui.btnTempDataInd.clicked.connect(self.load_temperature_data)
        self.ui.btnPressureDataLoad.clicked.connect(self.load_pressure_data)
        self.ui.btnPressureDataInd.clicked.connect(self.load_pressure_data)
        self.ui.btnLoadData.clicked.connect(self.refresh_data_files)

        # Connect section buttons to their respective functions
        self.ui.btnTestPrereq.clicked.connect(lambda: self.handle_section_change("Test Prerequisite"))
        self.ui.btnBasicInfo.clicked.connect(lambda: self.update_content(self.ui.basicInformation))
        self.ui.btnSysSpec.clicked.connect(lambda: self.update_content(self.ui.systemSpecification))
        self.ui.btnPropSpec.clicked.connect(lambda: self.update_content(self.ui.propellantSpecification))
        self.ui.btnCatSpec.clicked.connect(lambda: self.update_content(self.ui.catalystSpecification))
        self.ui.btnCompDet.clicked.connect(lambda: self.update_content(self.ui.componentDetails))
        self.ui.btnTestDet.clicked.connect(lambda: self.update_content(self.ui.testDetails))

        self.ui.btnHtrOp.clicked.connect(lambda: self.handle_section_change("Heater Operation"))
        self.ui.btnHtrInfo.clicked.connect(lambda: self.update_content(self.ui.heaterInformation))
        self.ui.btnHtrCyc.clicked.connect(lambda: self.update_content(self.ui.heaterCycles))

        self.ui.btnCycleNext.clicked.connect(lambda: self.change_heater_cycle_tab('next'))
        self.ui.btnCycleBack.clicked.connect(lambda: self.change_heater_cycle_tab('back'))

        self.ui.btnPstTestAn.clicked.connect(lambda: self.handle_section_change("Post Test Analysis"))
        self.ui.btnPstTestObs.clicked.connect(lambda: self.update_content(self.ui.postTestingObs))
        self.ui.btnCatPostAn.clicked.connect(lambda: self.update_content(self.ui.catalystPostAna))
        self.ui.btnPropPostAn.clicked.connect(lambda: self.update_content(self.ui.propellantPostAn))

        self.ui.btnPlots.clicked.connect(lambda: self.handle_section_change("PlotWindow"))

        self.ui.btnPerformance.clicked.connect(lambda: self.handle_section_change("Performance"))

        self.ui.btnTestAuthorization.clicked.connect(
            lambda: self.ui.contentStack.setCurrentWidget(self.ui.testAuthorization))

        # Temperature matrix button
        self.ui.btnTempMatrix.clicked.connect(self.handle_temp_matrix_clicked)

        # Connect plot button
        self.ui.pressurePlotBtn.clicked.connect(self.create_pressure_plot)
        self.ui.tempPlotBtn.clicked.connect(self.create_temperature_plot)
        self.ui.comboBoxYAxisTemp.itemCheckStateChanged.connect(self.create_temperature_plot)
        self.ui.comboBoxYAxisPressure.itemCheckStateChanged.connect(self.create_pressure_plot)

        # Connecting the include in report button to the plot preview and save
        self.ui.btnIncludeInReport.clicked.connect(self.handle_plot_inclusion)

        # Performance Calculation
        self.ui.btnCalculate.clicked.connect(self.calculate_performance)
        self.ui.btnPerformance.clicked.connect(lambda: self.update_content(self.ui.performance))

        # Generate Maximum temperatures plot
        self.ui.btnMaxTempsPlot.clicked.connect(self.generate_max_temperatures_plot)

        # Connecting the Next and Back buttons for Section view change
        self.ui.nextBtn.setShortcut(QKeySequence(Qt.Key_Right))
        self.ui.backBtn.setShortcut(QKeySequence(Qt.Key_Left))
        self.ui.nextBtn.clicked.connect(lambda: self.change_page(next=True))
        self.ui.backBtn.clicked.connect(lambda: self.change_page(next=False))

        # Connecting report generation
        self.ui.showReport.clicked.connect(self.generate_report)

        # Saving data to the database
        self.ui.saveDataToDatabase.clicked.connect(self.save_to_database)

        # Connecting Update table button with RI Concentration table
        self.ui.btnUpdateTable.clicked.connect(self.update_table)

        # Initially hide plot controls and meta data frame
        self.ui.plotControlsFrame.hide()
        self.ui.htrOpSubSections.hide()
        self.ui.pstTestAnSubSections.hide()
        self.ui.plotControlsFrame.hide()
        self.ui.pressurePlotSetti.hide()
        self.ui.lblAim.hide()
        self.ui.testNoFrame.hide()
        self.ui.lblPropellant.hide()
        self.ui.lblCatalyst.hide()
        self.ui.perforSubSections.hide()
        self.ui.tempMatrixDataSelection.hide()

        # Connect database icon button to load test data dialog
        self.existing_data_dialog.ui.btnBrowseDatabase.clicked.connect(self.load_test_data)

        # Connect file browser icon button to select the json file
        self.existing_data_dialog.ui.btnBrowseJSONFile.clicked.connect(self.load_data_from_json_file)

    def refresh_data_files(self):
        # Store the current color assignments before resetting
        color_assignments = self.color.copy() if hasattr(self, 'color') else {}

        # Clearing out all the line Edits filled for earlier test
        self.reset_input_fields()

        # Restore the color assignments
        self.color = color_assignments

        # Clear existing plot
        while self.plot_layout.count():
            item = self.plot_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.clear_plot_previews()

        # Delete if there's temporary folder storing plots of previous analysis exists
        self.delete_temp_report_folder()

        # Delete if there's temporary plots folder created from database
        if hasattr(self, 'db_handler') and self.db_handler is not None:
            self.db_handler.delete_temp_plots_folder()

        # Resetting the data load button in the left panel
        self.ui.btnTempDataInd.setStyleSheet(u'''QPushButton{
    	                                                background-color: rgba(4, 120, 87, 0.3);
    	                                                padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#47a08e;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnPressureDataInd.setStyleSheet(u'''
                                                    QPushButton{
                                                        	background-color:rgba(29, 78, 216, 0.3);
                                                        	padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#7b92d4;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnTempDataLoad.setStyleSheet(u'''QPushButton{
    	                                                    background-color: rgba(4, 120, 87, 0.3);
    	                                                    padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#47a08e;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnPressureDataLoad.setStyleSheet(u'''QPushButton{
    	                                                    background-color: rgba(29, 78, 216, 0.3);
    	                                                    padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#7b92d4;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.subLnEdtTestConductedBy.clear()
        self.ui.subLnEdtReportGeneratedBy.clear()
        self.ui.subLnEdtReportAuthorizedBy.clear()

    def value_changed(self, parameter_whose_value_changed, type_of_parameter):

        dict_of_parameters = {'prop_initial': [self.ui.lnEdtInitialPropMass, self.ui.subLnEdtWghtOfPropBefTest, self.ui.subLnEdtPropWghtFild_2],
                              'prop_final': [self.ui.lnEdtFinalPropMass, self.ui.subLnEdtWghtOfPropAftTest, self.ui.subLnEdtPropWghtRecvrd_2],
                              'cat_initial': [self.ui.subLnEdtCatWghtBefTest, self.ui.subLnEdtCatWghtFild],
                              }

        if type_of_parameter == 'cat_final':
            cat_initial = float(self.ui.subLnEdtCatWghtFild.value())
            cat_final = float(self.ui.subLnEdtCatWghtRecvrd.value())
            cat_loss_percentage = ((cat_final - cat_initial)/cat_initial) * 100
            self.ui.subLnEdtCatLosPerc.setValue(cat_loss_percentage)
        elif type_of_parameter == 'mass_flow_rate':
            initial_prop_mass = self.ui.subLnEdtPropWghtFild_2.value()
            final_prop_mass = self.ui.subLnEdtPropWghtRecvrd_2.value()
            burn_time = self.ui.subLnEdtFirgDur_2.value()
            mass_flow_rate = ((initial_prop_mass - final_prop_mass)/burn_time) * 1000     # milli grams per second
            self.ui.subLnEdtApproxMassFlowRate_2.setValue(mass_flow_rate)
        elif type_of_parameter == 'test_no':
            self.ui.testNoFrame.setVisible(True)
            text = self.ui.subLnEdtTestNo.text()
            self.ui.lblTestNumber.setText(text)
        elif type_of_parameter == 'aim':
            self.ui.lblAim.setVisible(True)
            text = self.ui.subLnEdtAim.text()
            self.ui.lblAim.setText(text)
        elif type_of_parameter == 'propellant':
            self.ui.lblPropellant.setVisible(True)
            text = self.ui.subLnEdtProp.text()
            self.ui.lblPropellant.setText(text)
        elif type_of_parameter == 'catalyst':
            text = self.ui.subLnEdtCat.text()
            self.ui.lblCatalyst.setVisible(True)
            self.ui.lblCatalyst.setText(text)
        elif type_of_parameter == 'prop_RI':
            current_text_prop = self.ui.subLnEdtProp.text()
            current_text_prop_RI = self.ui.subLnEdtPropRI.value()
            prop_concentration = self.concentration_by_RI(current_text_prop_RI)
            prop_concentration = prop_concentration
            set_text = current_text_prop + ' | ' + str(prop_concentration) + '%'
            self.ui.lblPropellant.setText(set_text)
            self.ui.subLnEdtPropRIBefFirg_2.setValue(float(self.ui.subLnEdtPropRI.value()))
            self.ui.subLnEdtConcBefTest.setValue(prop_concentration)
        else:
            value = parameter_whose_value_changed.value()
            l = dict_of_parameters[type_of_parameter]
            for i in l:
                if i != parameter_whose_value_changed:
                    i.setValue(value)

        if (self.ui.lnEdtInitialPropMass.value() != 0) and (self.ui.lnEdtFinalPropMass.value() >= 0):
            prop_initial = float(self.ui.lnEdtInitialPropMass.value())
            prop_final = float(self.ui.lnEdtFinalPropMass.value())
            prop_used_percentage = ((prop_initial - prop_final) / prop_initial) * 100
            self.ui.subLnEdtPropUsedPerc_2.setValue(prop_used_percentage)

            if self.ui.subLnEdtFirgDur_2.value() is not None:
                initial_prop_mass = self.ui.subLnEdtPropWghtFild_2.value()
                final_prop_mass = self.ui.subLnEdtPropWghtRecvrd_2.value()
                burn_time = self.ui.subLnEdtFirgDur_2.value()
                mass_flow_rate = ((initial_prop_mass - final_prop_mass) / burn_time) * 1000  # milli grams per second
                self.ui.subLnEdtApproxMassFlowRate_2.setValue(mass_flow_rate)

    def reset_button_styles(self):
        """Reset all button styles to default"""
        # Main section buttons
        main_buttons = [
            self.ui.btnTestPrereq,
            self.ui.btnHtrOp,
            self.ui.btnPstTestAn,
            self.ui.btnPlots,
            self.ui.btnPerformance,
            self.ui.btnTestAuthorization,
        ]

        # Subsection buttons
        sub_buttons = [
            self.ui.btnBasicInfo,
            self.ui.btnSysSpec,
            self.ui.btnPropSpec,
            self.ui.btnCatSpec,
            self.ui.btnCompDet,
            self.ui.btnTestDet,
            self.ui.btnHtrInfo,
            self.ui.btnHtrCyc,
            self.ui.btnPstTestObs,
            self.ui.btnCatPostAn,
            self.ui.btnPropPostAn,
        ]

        # Reset all button styles
        for button in main_buttons:
            button.setStyleSheet(self.default_button_style)

        for button in sub_buttons:
            button.setStyleSheet(self.default_button_style_subsections)

    def update_section_visibility(self, current_page: str):
        """Update the visibility of subsection panels"""
        # Hide all subsection panels first
        self.ui.testPrereqSubSections.hide()
        self.ui.htrOpSubSections.hide()
        self.ui.pstTestAnSubSections.hide()
        self.ui.plotControlsFrame.hide()
        self.ui.perforSubSections.hide()

        # Show the relevant subsection panel based on the current page
        if current_page in self.section_headers:
            section = self.section_headers[current_page]
            if section == "Test Prerequisite":
                self.ui.testPrereqSubSections.show()
            elif section == "Heater Operation":
                self.ui.htrOpSubSections.show()
            elif section == "Post Test Analysis":
                self.ui.pstTestAnSubSections.show()
            elif section == "Plots":
                # Fix DataFrame boolean evaluation
                has_temperature_data = False if self.temperature_data is None else not self.temperature_data.empty
                has_pressure_data = False if self.pressure_data is None else not self.pressure_data.empty

                if not (has_temperature_data or has_pressure_data):
                    QMessageBox.warning(self, 'Load Data', 'Please load the Temperature or Pressure Data first!')
                    # Revert to previous page
                    previous_widget = self.ui.contentStack.widget(self.ui.contentStack.currentIndex() - 1)
                    self.ui.contentStack.setCurrentWidget(previous_widget)
                    previous_page = previous_widget.objectName()
                    return
                self.ui.plotControlsFrame.show()
            elif section == "Performance":
                self.ui.perforSubSections.show()

    def change_heater_cycle_tab(self, direction: str = None):
        if direction == 'next':
            current_tab = self.ui.cycleTabWidget.currentIndex()
            to_set_tab = current_tab + 1
            to_set_tab = to_set_tab % 4
        elif direction == 'back':
            current_tab = self.ui.cycleTabWidget.currentIndex()
            to_set_tab = current_tab - 1
            to_set_tab = to_set_tab % 4

        self.ui.cycleTabWidget.setCurrentIndex(to_set_tab)

    def update_button_states(self, current_page: str):
        """Update button styles based on current page"""
        # Reset all buttons to default style
        self.reset_button_styles()

        # Get the buttons to highlight
        if current_page in self.page_button_mapping:
            main_button, sub_button = self.page_button_mapping[current_page]

            # Highlight the main section button
            if main_button:
                main_button.setStyleSheet("""
                                        background-color:#00b28e;
                                        font-size: 19px;
	                                    font-family: Helvetica;
	                                    font-weight: bold;
                                        color:black;
                                        """
                                          )

            # Highlight the subsection button if it exists
            if sub_button:
                sub_button.setStyleSheet(self.selected_button_style)

    def change_page(self, next: bool):
        """Handle next and back push button click"""
        current_index = self.ui.contentStack.currentIndex()
        total_pages = self.ui.contentStack.count()

        # Calculate new index based on direction
        if next:
            # Don't proceed if already at last page
            if current_index >= total_pages - 1:
                return
            index = current_index + 1
        else:
            # Don't proceed if already at first page
            if current_index <= 0:
                return
            index = current_index - 1

        # Get the target page name
        target_widget = self.ui.contentStack.widget(index)
        target_page = target_widget.objectName()

        # Check if trying to navigate to plots section without data
        if target_page == "PlotWindow" and not (self.temperature_data or self.pressure_data):
            QMessageBox.warning(self, 'Load Data', 'Please load the Temperature or Pressure Data first!')
            return  # Don't proceed with page change

        # If we get here, it's safe to change the page
        self.ui.contentStack.setCurrentIndex(index)

        # Update UI elements - Note the order here is important
        self.update_section_visibility(target_page)  # This will handle reverting if necessary
        self.update_button_states(target_page)

        # Update section header only if we didn't revert in update_section_visibility
        if self.ui.contentStack.currentWidget().objectName() == target_page:
            if target_page in self.section_headers:
                self.ui.lblCurentSection.setText(self.section_headers[target_page])

    def update_content(self, widget):
        self.ui.contentStack.setCurrentWidget(widget)

        # Get current page name
        current_page = self.ui.contentStack.currentWidget().objectName()

        # Update UI elements
        self.update_button_states(current_page)

        # Update section header
        if current_page in self.section_headers:
            self.ui.lblCurentSection.setText(self.section_headers[current_page])

    def get_executable_path(self):
        """Get the correct base path whether running as script or executable"""
        if getattr(sys, 'frozen', False):
            # Running as executable
            return os.path.dirname(sys.executable)
        else:
            # Running as script
            return os.getcwd()

    def concentration_by_RI(self, RI: float):
        return round((-3208.9 * RI ** 2) + (10135 * RI) - 7807.9 , 2)

    def update_table(self):
        # For updating the RI Concentation table of Propellant
        RI_before_test = self.ui.subLnEdtPropRIBefFirg_2.value()
        RI_after_test = self.ui.subLnEdtPropRIAftFirg_2.value()


        conc_before_test = str(self.concentration_by_RI(RI_before_test))
        conc_after_test = str(self.concentration_by_RI(RI_after_test))

        if RI_after_test == 0:
            RI_after_test = 'NA'
            conc_after_test = 'NA'

        self.ui.subLblPropBefRITable.setText(str(RI_before_test))
        self.ui.subLblPropAftRITable.setText(str(RI_after_test))

        self.ui.subLblPropBefConcTable.setText(conc_before_test)
        self.ui.subLblPropAftConcTable.setText(conc_after_test)

    def load_temperature_data(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Temperature Data File",
            "",
            "CSV files (*.csv)"
        )
        if not file_path:
            return

        # Try different encodings
        encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
        df_preview = None

        for encoding in encodings:
            try:
                # Read the data portion of CSV, skipping metadata
                df_preview = pd.read_csv(file_path, skiprows=6, nrows=0, encoding=encoding)
                # If successful, use this encoding for the rest of the operations
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error reading file with {encoding} encoding: {str(e)}")
                continue

        if df_preview is None:
            QMessageBox.critical(self, "Error", "Could not read the CSV file with any supported encoding.")
            return

        columns = df_preview.columns.tolist()

        # Try to detect scan rate from metadata using the same encoding
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                metadata_lines = [next(f).strip() for _ in range(6)]
            scan_rate = None
            for line in metadata_lines:
                if 'Scan Rate' in line:
                    try:
                        scan_rate = int(line.split(':')[1].split(',')[0].strip())
                    except (ValueError, IndexError):
                        print(f"Warning: Invalid scan rate value: {line}")
                    break

            # Show configuration dialog
            dialog = TemperatureDataConfigDialog(columns, scan_rate, self)
            if dialog.exec() == QDialog.Accepted:
                sample_col = dialog.get_sample_column()
                temp_cols = dialog.get_temperature_columns()
                scan_rate = dialog.get_scan_rate()

                # Validate selections
                if not sample_col or not temp_cols or scan_rate is None or scan_rate <= 0:
                    QMessageBox.warning(self, "Invalid Selection",
                                        "Please select a Sample column, at least one Temperature column, and provide a valid scan rate.")
                    return

                # Load data with selected columns using the successful encoding
                df = pd.read_csv(file_path, skiprows=6, usecols=[sample_col] + temp_cols, encoding=encoding)

                # Create time column
                df['time'] = df[sample_col] / scan_rate
                df = df.drop(columns=[sample_col])

                # Process temperature columns
                for col in temp_cols:
                    if df[col].dtype == object:
                        df[col] = df[col].astype(str).str.replace(',', '.')
                    df[col] = pd.to_numeric(df[col], errors='coerce')

                self.temperature_data = df[['time'] + temp_cols]

                # Create necessary directories
                base_path = self.get_executable_path()
                temp_report_dir = os.path.join(base_path, "temp_report")
                plots_dir = os.path.join(temp_report_dir, "plots")
                temp_plot_dir = os.path.join(plots_dir, "temperature")

                # Create all required directories
                os.makedirs(temp_report_dir, exist_ok=True)
                os.makedirs(plots_dir, exist_ok=True)
                os.makedirs(temp_plot_dir, exist_ok=True)

                # Ensure directories are writable
                for directory in [temp_report_dir, plots_dir, temp_plot_dir]:
                    if not os.access(directory, os.W_OK):
                        os.chmod(directory, 0o777)

                # Analyze and generate only 'all_temperature' plot
                self.temp_analyzer.analyze_temperature_data(self.temperature_data, temp_plot_dir, save_plot=True,
                                                            plot_types=['all'])

                # Verify plots were created
                if os.path.exists(temp_plot_dir):
                    plots = os.listdir(temp_plot_dir)
                    if plots:
                        self.ui.lblLogInfo.setText("Temperature plots created successfully")

                        # Adding default plots to preview after they're generated
                        self.add_default_plots_to_preview()
                    else:
                        self.ui.lblLogInfo.setText("No plots were generated")
                else:
                    self.ui.lblLogInfo.setText("Plot directory not created")

                # Enable the plots button
                self.ui.btnPlots.setEnabled(True)
                self.ui.btnTempMatrix.setEnabled(True)
                self.setup_plot_controls()
                self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')

                # Call check_and_enable_plots_button to ensure the plots button is enabled
                if hasattr(self, 'auto_saver'):
                    self.auto_saver.check_and_enable_plots_button()

                # Update the burn time lbl on UI
                burn_time = self.temperature_data['time'].iloc[-1]
                self.ui.lblFiringDuration.setText(f'{burn_time}s')
                self.ui.subLnEdtFirgDur_2.setValue(burn_time)

                QMessageBox.information(self, "Data Loading Status",
                                        f"Temperature Data loaded successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error processing file: {str(e)}")
            import traceback
            traceback.print_exc()
            return

    #---/---/---/---/---/---/---Temperature Matrix---/---/---/---/---/---/---/---/---/---/---/---/---/---/
    def create_interactive_temperature_plot(self):
        """Create interactive temperature plot with range selection"""
        try:
            if self.temperature_data is None:
                self.ui.lblLogInfo.setText("Please load temperature data first!")
                return

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Setting the current section name in the top bar
            self.ui.lblCurentSection.setText('Temperature Matrix Plot')

            # Get selected temperature columns
            selected_cols = self.temp_selection_widget.get_selected_columns()

            # Create figure and canvas
            self.figure, self.axes = plt.subplots(figsize=(6, 4.5))

            # Plot each selected temperature column with color from temperature selection widget
            for col in selected_cols:
                # Get color from the temperature selection widget
                color = self.temp_selection_widget.get_column_color(col)

                self.axes.plot(self.temperature_data['time'],
                               self.temperature_data[col],
                               label=col,
                               linewidth=2,
                               color=color
                               )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)
            self.axes.patch.set_alpha(0.0)

            # Apply plot styling
            self.plot_style(self.axes, 'Time (s)', 'Temperature (°C)',
                            'Temperature Distribution (Click and drag to select valid ranges)')

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Store the figure
            self.current_figures['temperature'] = self.figure

            # Create canvas and ensure it has a transparent background
            self.canvas = PlotCanvas(self.figure)
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent

            # Create layout for plot and toolbar
            plot_container = QWidget()
            plot_layout = QVBoxLayout(plot_container)
            plot_layout.setContentsMargins(0, 0, 0, 0)
            plot_container.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_container)

            data_series = {col: col for col in selected_cols}

            self.toolbar = CustomNavigationToolbar(self.canvas, plot_container, data_series)

            plot_layout.addWidget(self.toolbar)
            plot_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Creating annotation
            self.cursor_annotation = self.axes.annotate(
                '',
                xy=(0, 0),
                xytext=(10, 10),
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', fc='black', alpha=0.7),
                color='white',
                fontsize=9
            )
            self.cursor_annotation.set_visible(False)

            self.axes.title.set_color('#000000')

            # Connect event handlers
            self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
            self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
            self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)
            self.canvas.mpl_connect('motion_notify_event', self.update_cursor_info)

            # Add control buttons
            button_layout = QHBoxLayout()

            # Undo button
            undo_button = QPushButton("Undo Last Selection")
            undo_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #557799;
                            }
                        """)
            undo_button.clicked.connect(self.undo_last_range)
            button_layout.addWidget(undo_button)

            # Clear button
            clear_button = QPushButton("Clear All Selections")
            clear_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #aa5555;
                            }
                        """)
            clear_button.clicked.connect(self.clear_all_ranges)
            button_layout.addWidget(clear_button)

            # Generate Matrix button
            generate_button = QPushButton("Generate Matrix")
            generate_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #55aa55;
                            }
                        """)
            generate_button.clicked.connect(self.generate_filtered_matrix)
            button_layout.addWidget(generate_button)

            # Add buttons to plot layout
            button_widget = QWidget()
            button_widget.setLayout(button_layout)
            self.plot_layout.addWidget(button_widget)

            self.ui.lblLogInfo.setText(
                "Interactive plot created. Click and drag to select valid ranges for temperature Matrix.")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating interactive plot: {str(e)}")

    def update_cursor_info(self, event):
        """Update cursor position information with smart annotation positioning"""
        if event.inaxes == self.axes:
            x, y = event.xdata, event.ydata

            # Find closest data points
            time_idx = (self.temperature_data['time'] - x).abs().idxmin()
            current_values = {}

            # Get values for all plotted columns
            selected_cols = self.temp_selection_widget.get_selected_columns()
            for col in selected_cols:
                current_values[col] = self.temperature_data.loc[time_idx, col]

            # Format annotation text
            text = f'Time: {x:.2f}s\n'
            for col, val in current_values.items():
                text += f'{col}: {val:.2f}°C\n'

            # Get the bounds of the plot
            bbox = self.axes.get_position()
            y_range = self.axes.get_ylim()

            # Calculate relative position in the plot
            y_rel = (y - y_range[0]) / (y_range[1] - y_range[0])

            # Determine annotation position
            if y_rel > 0.5:  # If cursor is in upper half
                xytext = (10, -15 - 10 * len(current_values))  # Place below cursor
                va = 'top'
            else:  # If cursor is in lower half
                xytext = (10, 15)  # Place above cursor
                va = 'bottom'

            # Update annotation
            self.cursor_annotation.set_text(text)
            self.cursor_annotation.xy = (x, y)
            self.cursor_annotation.xyann = xytext
            self.cursor_annotation.set_va(va)
            self.cursor_annotation.set_visible(True)
            self.canvas.draw_idle()
        else:
            self.cursor_annotation.set_visible(False)
            self.canvas.draw_idle()

    def update_temperature_plot(self):
        """Update plot when temperature selection changes"""
        if hasattr(self, 'axes') and self.axes is not None:
            try:
                self.axes.clear()
            except Exception as e:
                print(f"Error clearing axes: {str(e)}")
                return  # Exit the method if we can't clear the axes

            # Check if temperature_data exists and is not None
            if not hasattr(self, 'temperature_data') or self.temperature_data is None:
                print("Temperature data is not available")
                return  # Exit the method if temperature data is not available

            selected_cols = self.temp_selection_widget.get_selected_columns()

            # Plot each selected temperature column with color from temperature selection widget
            for col in selected_cols:
                try:
                    # Get color from the temperature selection widget
                    color = self.temp_selection_widget.get_column_color(col)

                    self.axes.plot(self.temperature_data['time'],
                                   self.temperature_data[col],
                                   label=col,
                                   linewidth=2,
                                   color=color
                                   )
                except Exception as e:
                    print(f"Error plotting column {col}: {str(e)}")

            # Redraw Included ranges
            try:
                if hasattr(self, 'selected_ranges') and self.selected_ranges:
                    for start, end in self.selected_ranges:
                        try:
                            height = self.axes.get_ylim()[1] - self.axes.get_ylim()[0]
                            rect = Rectangle(
                                (start, self.axes.get_ylim()[0]),
                                end - start, height,
                                alpha=0.2,
                                facecolor='green'
                            )
                            self.axes.add_patch(rect)
                        except Exception as e:
                            print(f"Error drawing range rectangle: {str(e)}")
            except Exception as e:
                print(f"Error processing selected ranges: {str(e)}")

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background

            x_label = 'Time (s)'
            y_label = 'Temperature (°C)'
            title = 'Temperature Distribution (Click and drag to select ranges to exclude)'

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, y_label, title)

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Redraw the plot
            self.canvas.draw()

    def on_mouse_press(self, event):
        """Handle mouse press for range selection"""
        if event.inaxes != self.axes or not event.button == 1:
            return

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            return

        self.selecting = True
        self.start_x = event.xdata

        # Create selection rectangle
        height = self.axes.get_ylim()[1] - self.axes.get_ylim()[0]
        self.current_rect = Rectangle(
            (event.xdata, self.axes.get_ylim()[0]),
            0, height,
            alpha=0.3,
            facecolor='green'
        )
        self.axes.add_patch(self.current_rect)

    def on_mouse_motion(self, event):
        """Handle mouse motion during range selection"""
        if not self.selecting or event.inaxes != self.axes or not self.current_rect:
            return

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            return

        # Update rectangle width
        width = event.xdata - self.start_x
        self.current_rect.set_width(width)
        self.canvas.draw_idle()  # More efficient than full draw

    def on_mouse_release(self, event):
        """Handle mouse release to complete range selection"""
        if not self.selecting:
            return

        self.selecting = False

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            if self.current_rect:
                self.current_rect.remove()
                self.canvas.draw()
            return

        if event.inaxes != self.axes or not self.current_rect:
            if self.current_rect:
                self.current_rect.remove()
                self.canvas.draw()
            return

        # Add range to selected ranges
        x_range = sorted([self.start_x, event.xdata])
        if abs(x_range[1] - x_range[0]) > 0.1:  # Minimum selection width
            self.selected_ranges.append((x_range[0], x_range[1]))
            self.current_rect.set_alpha(0.2)
            self.ui.lblLogInfo.setText(
                f"Added selected range: {x_range[0]:.2f}s to {x_range[1]:.2f}s"
            )
        else:
            self.current_rect.remove()
            self.canvas.draw()

        self.current_rect = None

    def undo_last_range(self):
        """Remove the last selected range"""
        if not self.selected_ranges:
            return

        self.selected_ranges.pop()

        self.update_temperature_plot()
        self.ui.lblLogInfo.setText("Removed last excluded range")

    def clear_all_ranges(self):
        """Clear all selected ranges"""
        self.selected_ranges.clear()
        self.ui.lblLogInfo.setText("Cleared all excluded ranges")

        # Update the plot
        self.update_temperature_plot()

    def generate_filtered_matrix(self):
        """Generate temperature analysis matrix using selected ranges"""
        try:
            # Changing the content window to the temperature matrix
            self.ui.contentStack.setCurrentWidget(self.ui.tableView)

            # Get selected columns
            selected_cols = self.temp_selection_widget.get_selected_columns()

            if not selected_cols:
                QMessageBox.warning(self, "Warning",
                                    "Please select at least one temperature column.")
                return

            if not self.selected_ranges:
                QMessageBox.warning(self, "Warning",
                                    "Please select at least one time range.")
                return

            # Store the selected columns
            self.selected_cols = selected_cols  # Update selected_cols here

            # Create mask for selected ranges
            mask = pd.Series(False, index=self.temperature_data.index)

            for start, end in self.selected_ranges:
                mask |= ((self.temperature_data['time'] >= start) &
                         (self.temperature_data['time'] <= end))

            # Filter data
            filtered_data = self.temperature_data[mask]

            # Select only chosen columns plus time
            columns_to_use = ['time'] + selected_cols
            filtered_data = filtered_data[columns_to_use]

            self.filtered_temp_data = filtered_data

            # Generate matrix
            output_dir = os.path.join(os.getcwd(), "temp_report")
            os.makedirs(output_dir, exist_ok=True)

            self.temp_analyzer.generate_temperature_table(filtered_data, output_dir)

            if hasattr(self.temp_analyzer, 'analysis_results') and 'matrix' in self.temp_analyzer.analysis_results:
                self.display_temperature_analysis(self.temp_analyzer.analysis_results['matrix'])
                self.ui.lblLogInfo.setText("Generated matrix from selected ranges")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error generating matrix: {str(e)}")
            traceback.print_exc()

    def generate_max_temperatures_plot(self):
        try:
            self.current_tab_name = 'temperature'
            self.current_plot_type = 'temperature_matrix'

            # Set the plot title
            plot_title = 'Max Temperature Distribution Across Locations'

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot and store reference to prevent deletion
            self.clear_plot_layout()

            temp_cols = [col for col in self.filtered_temp_data.columns if col not in ['time']]
            max_temps = [(col, self.filtered_temp_data[col].max()) for col in temp_cols]
            max_temps = [(col, temp) for col, temp in max_temps if not pd.isna(temp)]

            if max_temps:
                self.ui.plotTopBar.show()
                self.current_figures.clear()

                # Create figure with adjusted size and spacing
                self.figure, self.axes = plt.subplots(figsize=(3, 1))

                temps = [temp for _, temp in max_temps]
                locations = [col for col, _ in max_temps]

                # Get color for Maximum Temperature using the color utility
                max_temp_color = assign_color('Maximum Temperature', self.color, self.color_palette)

                self.axes.plot(range(len(temps)), temps, '-o',
                               linewidth=2, markersize=8,
                               color=max_temp_color,
                               label='Maximum Temperature')

                # Add point labels with adjusted position
                for i, (_, val) in enumerate(max_temps):
                    self.axes.annotate(f'{val:.2f}°C',
                                       (i, val),
                                       xytext=(0, 10),
                                       textcoords='offset points',
                                       ha='center',
                                       va='bottom')

                # Set figure and axes backgrounds to transparent
                self.figure.patch.set_alpha(0.0)  # Transparent figure background
                self.axes.patch.set_alpha(0.0)  # Transparent axes background

                # Set labels and style
                plt.xticks(range(len(temps)), locations, rotation=45, ha='right')
                x_label = 'Location'
                y_label = 'Maximum Temperature (°C)'

                # Applying plot styling
                self.plot_style(self.axes, x_label, y_label, plot_title)

                legend = self.axes.legend(bbox_to_anchor=(0.1, 1),
                                          loc='upper left',
                                          facecolor='none',  # Transparent background
                                          edgecolor='#446699'  # Border color
                                          )
                # Setting legend text color
                for text in legend.get_texts():
                    text.set_color('#09090b')

                # Tight layout to avoid extra padding
                # self.figure.tight_layout()

                # Store the figure
                self.current_figures['temperature_matrix'] = self.figure

                # Create canvas and ensure it has a transparent background
                self.canvas = PlotCanvas(self.figure)
                self.canvas.setStyleSheet("background-color: transparent;")

                plot_widget = QWidget()
                plot_widget_layout = QVBoxLayout(plot_widget)
                plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
                self.plot_layout.addWidget(plot_widget)

                data_series = {'Maximum Temperature': temps}
                toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_series)

                plot_widget_layout.addWidget(toolbar)
                plot_widget_layout.addWidget(self.canvas)

                # Set parent widget background to transparent if needed
                self.ui.plots.setStyleSheet("background-color: transparent;")

                # Update the plot title
                self.update_current_plot_title()

                # Set the title explicitly on the axes
                self.axes.set_title(plot_title,
                                    color='#09090b',
                                    fontsize=12,
                                    fontweight='bold',
                                    y=1.06)

                # Store the current plot information
                self.current_plot_info = {
                    'title': plot_title,
                    'type': 'temperature_matrix'
                }

                self.ui.lblLogInfo.setText("Maximum temperatures plot created successfully")

                # Setting indicator color green
                self.ui.tempMatrixIndicator.setStyleSheet("""
                background-color: rgb(6, 196, 142);
                border-radius: 7px;
                """
                                                          )

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    # Method to toggle Visibibility of ui elements
    @staticmethod
    def toggle_visibility(element):
        element.setVisible(not element.isVisible())

    def toggle_section(self, section_key: str, widget: QWidget):
        """Toggle section with animation"""
        try:
            animation = self.section_animations.get(section_key)
            if animation:
                # Reset any ongoing animations
                animation.animation.stop()
                animation.opacity_animation.stop()

                if widget.isHidden() or widget.maximumHeight() == 0:
                    # Ensure widget is in proper state before expanding
                    widget.setMaximumHeight(16777215)  # Reset max height
                    animation.expand()
                else:
                    animation.collapse()

        except Exception as e:
            print(f"Error toggling section: {str(e)}")

    def handle_temp_matrix_clicked(self):
        """Handle temperature matrix button click"""

        # Hiding plot top bar
        self.ui.plotTopBar.hide()

        # Set visible the temperature data selection window
        self.toggle_visibility(self.ui.tempMatrixDataSelection)

        # Switch to plots widget
        self.ui.contentStack.setCurrentWidget(self.ui.plots)
        self.ui.lblCurentSection.setText("Plots")

        # Show temperature selection widget
        self.temp_selection_widget.show()

        # Get temperature columns
        temp_cols = [col for col in self.temperature_data.columns
                     if col not in ['Sample', 'time']]

        # Initialize selection widget with columns
        self.temp_selection_widget.add_temperature_columns(temp_cols)

        # Create initial plot
        self.create_interactive_temperature_plot()

#-/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/

    def setup_plot_controls(self):
        """Setup plot controls with validated columns"""
        try:
            if self.temperature_data is not None:
                valid_cols = [col for col in self.temperature_data.columns
                              if col != 'Sample']

                # Update X-axis combo box
                self.ui.comboBoxXAxisTemp.clear()
                self.ui.comboBoxXAxisTemp.addItems(valid_cols)

                # Set default X-axis to 'time' if available
                if 'time' in valid_cols:
                    self.ui.comboBoxXAxisTemp.setCurrentText('time')

                # Update Y-axis options
                self.ui.comboBoxYAxisTemp.clear()
                temp_cols = [col for col in valid_cols
                             if col not in ['Sample', 'time']]
                self.ui.comboBoxYAxisTemp.addItems(temp_cols)

                self.ui.lblLogInfo.setText("Plot controls updated:")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up plot controls: {str(e)}")

    def load_pressure_data(self):
        """Load pressure data and trigger temperature analysis"""
        try:

            file_path, _ = QFileDialog.getOpenFileName(self, "Select Pressure Data File", "",
                                                       "DAT and XLSX Files (*.dat *.xlsx)")
            pressure_absolute_dialog = Ui_Form()
            # pressure_absolute_dialog.setupUi(self)
            self.horizontalLayoutToggleButton = QHBoxLayout(pressure_absolute_dialog.toggle_botton_frame)
            pressure_absolute_toggle = MultiStateToggleSwitch(['Yes', 'No'])
            self.horizontalLayoutToggleButton.addWidget(pressure_absolute_toggle)


            if file_path:
                self.pressure_data = self.data_loader.load_pressure_data(file_path)
                if self.pressure_data is not None:
                    self.ui.lblLogInfo.setText("Pressure data loaded successfully")
                    self.ui.btnPlots.setEnabled(True)

                    # Changing the indicator color to green to show that the data is loaded
                    self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                    self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')

                    # Call check_and_enable_plots_button to ensure the plots button is enabled
                    if hasattr(self, 'auto_saver'):
                        self.auto_saver.check_and_enable_plots_button()

                    QMessageBox.information(self, "Data Loading Status",
                                            f"Pressure Data loaded Successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading pressure data: {str(e)}")

    def display_temperature_analysis(self, df: pd.DataFrame):
        """Display temperature analysis in GUI"""
        try:
            # Configure table widget
            table = self.ui.tableTemperatureAnalysis
            table.setRowCount(len(df.index))
            table.setColumnCount(len(df.columns))

            # Set headers
            table.setHorizontalHeaderLabels(df.columns)
            table.setVerticalHeaderLabels(df.index)

            # Populate data
            for i in range(len(df.index)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    item = QTableWidgetItem(str(value))
                    if i == j:  # Diagonal elements
                        item.setBackground(QColor(71, 160, 142))  # Green background
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                    table.setItem(j, i, item)

            # Adjust table appearance
            table.resizeColumnsToContents()
            table.resizeRowsToContents()
            table.show()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error displaying temperature analysis: {str(e)}")

    def _perform_scroll(self, widget):
        """Actually perform the scrolling operation"""
        try:
            scroll_area = widget.parent()
            while scroll_area and not isinstance(scroll_area, QScrollArea):
                scroll_area = scroll_area.parent()

            if scroll_area:
                widget_pos = widget.mapTo(scroll_area.widget(), QPoint(0, 0))
                scroll_area_height = scroll_area.height()
                widget_height = widget.height()
                center_position = max(0, widget_pos.y() - (scroll_area_height - widget_height) // 2)
                scroll_area.verticalScrollBar().setValue(center_position)

        except Exception as e:
            print(f"Error performing scroll: {str(e)}")

    def show_plot_settings(self, plot_type: str):
        """Show plot settings based on selected type and populate columns"""
        try:
            self.current_plot_type = plot_type

            # Create label for default plot preview window
            plot_label = QLabel()
            plot_label.setStyleSheet("""
                background-color: transparent;
                color:black;
            """)

            if plot_type == 'temp_matrix':
                # Set visible the temperature data selection window
                self.toggle_visibility(self.ui.tempMatrixDataSelection)

                # Switch to plot window
                self.ui.contentStack.setCurrentWidget(self.ui.plots)
                self.ui.lblCurentSection.setText("Temperature Matrix")
                self.create_temperature_matrix_plot()

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            if plot_type == 'temperature':

                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Temperature Plot')

                if self.temperature_data is not None:
                    columns = list(self.temperature_data.columns)
                    self.ui.comboBoxXAxisTemp.clear()
                    self.ui.comboBoxXAxisTemp.addItems(columns)
                    self.ui.comboBoxXAxisTemp.setCurrentText('time')
                    self.update_y_axis_options('temperature')

                    plot_label.setText('Temperature Plot Window')   # Default text in plot window frame

                    # Changing current tab name as temperature to facilitate the plot preview and inclusion in report
                    self.current_tab_name = 'temperature'
                else:
                    self.ui.lblLogInfo.setText("Please load the temperature data!")

            elif plot_type == 'pressure':
                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Pressure Plot')

                if self.pressure_data is not None:
                    columns = list(self.pressure_data.columns)
                    self.ui.comboBoxXAxisPressure.clear()
                    self.ui.comboBoxXAxisPressure.addItems(columns)
                    self.update_y_axis_options('pressure')
                    self.ui.pressurePlotSetti.show()


                    plot_label.setText('Pressure Plot Window')  # Default text in plot window frame
                    self.toggle_visibility(self.ui.pressurePlotBtnFrame)
                else:
                    self.ui.lblLogInfo.setText("Please load the pressure data!")

            plot_label.setAlignment(Qt.AlignCenter)

            # Adding label to plot layout for display
            self.plot_layout.addWidget(plot_label)

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up plot settings: {str(e)}")

    def create_tabbed_pressure_plot(self, data, x_col, y_cols, range_min=None, range_max=None):
        """Create pressure plots in separate tabs"""
        try:
            # Closing any existing figures before creating new ones
            plt.close('all')

            # Clearing any existing figures stored in current figures
            self.current_figures.clear()

            # Clear existing plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            data_series = {col: col for col in y_cols}

            fig_full, ax_full = plt.subplots(figsize=(3, 1))
            self.figure = fig_full
            self.axes = ax_full

            # Plot background transparent
            fig_full.patch.set_alpha(0.0)
            ax_full.patch.set_alpha(0.0)

            canvas_full = PlotCanvas(fig_full)
            self.canvas = canvas_full

            # Plot full range data with dynamically assigned colors
            for y_col in y_cols:
                # Get color for this column using our utility function
                color = assign_color(y_col, self.color, self.color_palette)

                ax_full.plot(data[x_col], data[y_col], label=y_col, linewidth=2, color=color)

            if range_min is not None and range_max is not None:
                # Highlight selected range
                ax_full.axvspan(range_min, range_max, color='green', alpha=0.2)

            x_label = self.ui.lnEdtXLabelPressure.text()
            y_label = self.ui.lnEdtYLabelPressure.text()

            if x_label == '':
                x_label = 'Time (s)'
                self.ui.lnEdtXLabelPressure.setText(x_label)
            if y_label == '':
                y_label = 'Pressure (mbar)'
                self.ui.lnEdtYLabelPressure.setText(y_label)

            self.plot_style(ax_full, x_label, y_label, 'Pressure Plot')

            # Store full range figure
            self.current_figures['full_range'] = fig_full

            # Create tab widget
            self.tab_widget = QTabWidget(self)
            self.tab_widget.setStyleSheet("""
                                    QTabWidget {
                        background-color: transparent;
                        border: none;
                        border-radius: 10px;
                    }

                    QWidget {
                        background-color: transparent;
                    }

                    QTabWidget::tab-bar {
                        alignment: center;
                    }

                    QTabBar::tab {
                        border-radius: 5px;
                        width: 120px;
                        height: 20px;
                        color: black;
                        font-size: 16px;
                        font-family: inter;
                        padding: 2px;
                        background-color: #ffffff;  /* Optional: White background for tabs */
                    }

                    QTabBar::tab:selected {
                        background: black;
                        color: white;
                    }

                    QTabBar::tab:hover {
                        background: #787878;
                    }

                    QTabWidget::pane {  /* Target the content area frame */
                        border: none;  /* Remove border */
                        background-color: transparent;  /* Make background transparent */
                        border-radius: 10px;  /* Match the tab widget's border radius */
                    }
                                """)

            self.tab_widget.setContentsMargins(0, 0, 0, 0)

            self.tab_widget.currentChanged.connect(self.update_current_tab_name)

            # Create Selected Range tab if range is specified
            if range_min >= 0 and range_max != 0:
                selected_range_tab = QWidget()
                selected_range_layout = QVBoxLayout(selected_range_tab)
                selected_range_tab.setStyleSheet("QWidget{background-color:#fcf1ff;}")

                # Create figure with same dimensions as full range
                fig_selected, ax_selected = plt.subplots(figsize=(3, 1))

                self.figure_selected = fig_selected
                self.axes_selected = ax_selected

                # Make plot background transparent
                fig_selected.patch.set_alpha(0.0)
                ax_selected.patch.set_alpha(0.0)

                canvas_selected = PlotCanvas(fig_selected)
                self.canvas_selected = canvas_selected

                # Create mask for selected range
                mask = (data[x_col] >= range_min) & (data[x_col] <= range_max)
                range_data = data[mask].copy()

                # Plot selected range data with dynamically assigned colors
                for y_col in y_cols:
                    # Get color for this column using our utility function
                    color = assign_color(y_col, self.color, self.color_palette)

                    ax_selected.plot(range_data[x_col], range_data[y_col],
                                     label=y_col, linewidth=2, color=color)

                    # Calculate and plot average
                    avg = range_data[y_col].mean()
                    self.axhline = ax_selected.axhline(y=avg, color='r', linestyle='--',
                                        label=f'{y_col} Avg = {avg:.2f} mbar')  # Unit for average pressure values displayed on the plot's legend



                self.plot_style(ax_selected, x_label, y_label, 'Pressure Plot - Selected Range')

                legend = ax_selected.legend(bbox_to_anchor=(1, 1),
                                            loc='upper left',
                                            facecolor='none',  # Transparent background
                                            edgecolor='#446699'  # Border color
                                            )

                # Setting legend text color
                for text in legend.get_texts():
                    text.set_color('#09090b')

                # Store selected range figure
                self.current_figures['selected_range'] = fig_selected

                # Add toolbar for selected range plot
                toolbar_selected = CustomNavigationToolbar(canvas_selected, selected_range_tab, data_series)
                selected_range_layout.addWidget(toolbar_selected)
                selected_range_layout.addWidget(canvas_selected)

                # Add selected range tab
                self.tab_widget.addTab(selected_range_tab, "Selected Range")

            # Creating Full Range tab
            full_range_tab = QWidget()
            full_range_layout = QVBoxLayout(full_range_tab)
            full_range_tab.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            full_range_tab.setContentsMargins(0, 0, 0, 0)

            # Add toolbar for full range plot
            toolbar_full = CustomNavigationToolbar(canvas_full, full_range_tab, data_series)
            full_range_layout.addWidget(toolbar_full)
            full_range_layout.addWidget(canvas_full)

            # Add tabs to widget
            self.tab_widget.addTab(full_range_tab, "Full Range")

            # Add tab widget to plot layout
            self.plot_layout.addWidget(self.tab_widget)

            # Update the current tab name initially
            self.update_current_tab_name(self.tab_widget.currentIndex())

            # Update the plot title dynamically
            self.update_current_plot_title()

            self.ui.lblLogInfo.setText("Pressure plots created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating pressure plots: {str(e)}")
            traceback.print_exc()
        finally:
            # Ensure any unused figures are closed
            for fig_num in plt.get_fignums():
                if plt.figure(fig_num) not in self.current_figures.values():
                    plt.close(fig_num)

    def update_current_plot_title(self):
        current_window = self.ui.lblCurentSection.text()
        plot_title = None

        if current_window == 'Pressure Plot':
            if self.tab_widget.count() > 1:
                if self.tab_widget.currentIndex() == 0:
                    plot_title = self.axes_selected.title.get_text()
                elif self.tab_widget.currentIndex() == 1:
                    plot_title = self.axes.title.get_text()
            elif self.tab_widget.count() == 1:
                plot_title = self.axes.title.get_text()
        else:
            plot_title = self.axes.title.get_text()

        self.ui.lnEditPlotTitle.setText(plot_title)

    def update_current_tab_name(self, index):
        if self.tab_widget is not None:
            self.current_tab_name = self.tab_widget.tabText(index)

        self.update_current_plot_title()

    def create_pressure_plot(self):
        try:
            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            if self.current_plot_type == 'pressure':
                x_col = self.ui.comboBoxXAxisPressure.currentText()
                y_cols = self.ui.comboBoxYAxisPressure.getCheckedItems()

                if x_col and y_cols:
                    # Get range values if provided
                    range_min = range_max = None
                    try:
                        if self.ui.lnEdtRangeMinPressure.text():
                            range_min = float(self.ui.lnEdtRangeMinPressure.text())
                        if self.ui.lnEdtRangeMaxPressure.text():
                            range_max = float(self.ui.lnEdtRangeMaxPressure.text())
                    except ValueError:
                        self.ui.lblLogInfo.setText("Invalid range values")
                        return

                    # Create tabbed pressure plots
                    self.create_tabbed_pressure_plot(
                        self.pressure_data,
                        x_col,
                        y_cols,
                        range_min,
                        range_max
                    )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    def plot_it(self, figsize: tuple, x_column, y_columns):

        # Creating figure with transparent background
        fig, ax = plt.subplots(figsize=figsize)

        for col in y_columns:
            # Get color for this column using our utility function
            color = assign_color(col, self.color, self.color_palette)

            ax.plot(self.temperature_data[x_column],
                    self.temperature_data[col],
                    label=col,
                    linewidth=2,
                    color=color
                    )
        return fig, ax

    def plot_style(self, ax, x_label, y_label, title):

        # Update plot settings
        ax.set_title(title,
                     color='#09090b',
                     fontsize=12,
                     fontweight='bold')

        ax.set_xlabel(x_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')
        ax.set_ylabel(y_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')

        legend = ax.legend(bbox_to_anchor=(0.5, -0.15),
                           loc='upper center',
                           ncol=5,
                           columnspacing=1,
                           handletextpad=0.5,
                           borderaxespad=0,
                           facecolor='none',  # Transparent background
                           edgecolor='#446699',  # Border color
                           bbox_transform=ax.transAxes
                           )
        # Setting legend text color
        for text in legend.get_texts():
            text.set_color('#09090b')

        # Style the axis numbers and grid
        ax.tick_params(axis='both',
                       colors='#09090b',  # White tick labels
                       labelsize=9)
        # Major grids
        ax.grid(which='major', linestyle='-', linewidth='0.75', alpha=0.6, color='#666666')  # Darker grid lines

        # Minor grids
        ax.minorticks_on()
        ax.grid(which='minor', linestyle=':', linewidth='0.5', color='#1e1e1e', alpha=0.6)

        ax.spines['top'].set_visible(True)
        ax.spines['right'].set_visible(True)

        # Style the spines (axis lines)
        for spine in ax.spines.values():
            spine.set_color('#446699')  # Custom spine color

    def create_temperature_plot(self):
        try:
            # Close any existing figures
            plt.close('all')

            # Clear existing plot layout
            self.clear_plot_layout()

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.current_figures.clear()  # Emptying any existing plots stored in it

            x_col = self.ui.comboBoxXAxisTemp.currentText()
            y_cols = self.ui.comboBoxYAxisTemp.getCheckedItems()

            data_series = {col: col for col in y_cols}

            # Create figure and axes with specific size (matching pressure plot)
            self.figure, self.axes = plt.subplots(figsize=(9, 3))

            # Plot data with transparent background and colors from temperature selection widget
            for col in y_cols:
                # Get color from the temperature selection widget if available, otherwise use fallback
                if hasattr(self, 'temp_selection_widget') and self.temp_selection_widget:
                    color = self.temp_selection_widget.get_column_color(col)
                else:
                    # Fallback color assignment
                    if col not in self.color:
                        color_index = len(self.color) % len(self.color_palette)
                        self.color[col] = self.color_palette[color_index]
                    color = self.color[col]

                self.axes.plot(self.temperature_data[x_col],
                               self.temperature_data[col],
                               label=col,
                               linewidth=2,
                               color=color
                               )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background

            # Set labels
            x_label = self.ui.lnEdtXLabelTemp.text()
            y_label = self.ui.lnEdtYLabelTemp.text()

            if x_label == '':
                x_label = 'Time (s)'
                self.ui.lnEdtXLabelTemp.setText(x_label)
            if y_label == '':
                y_label = 'Temperature (°C)'
                self.ui.lnEdtYLabelTemp.setText(y_label)

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, y_label, 'Temperature Plot')

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Store the figure
            self.current_figures['temperature'] = self.figure

            # Create canvas and ensure it has a transparent background
            self.canvas = PlotCanvas(self.figure)
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent

            plot_widget = QWidget()
            plot_widget_layout = QVBoxLayout(plot_widget)
            plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_widget)

            toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_series)

            self.plot_layout.addWidget(toolbar)

            plot_widget_layout.addWidget(toolbar)
            plot_widget_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Updating the plot title
            self.update_current_plot_title()

            self.ui.lblLogInfo.setText("Temperature plot created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()
        finally:
            # Ensure any unused figures are closed
            for fig_num in plt.get_fignums():
                if plt.figure(fig_num) not in self.current_figures.values():
                    plt.close(fig_num)

    def create_temperature_matrix_plot(self):
        """Create_plot to handle plot creation"""
        try:
            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.handle_temp_matrix_clicked()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    def handle_plot_inclusion(self):
        """Handle plot inclusion when button is clicked"""
        try:
            if not self.current_figures:
                self.ui.lblLogInfo.setText("No plots available to include")
                return

            # Get current plot information
            plot_info = self._get_current_plot_info()
            if not plot_info:
                return

            # Save plots based on type
            if self.ui.lblCurentSection.text() == 'Pressure Plot':
                self._handle_pressure_plot_inclusion(plot_info)
            elif self.ui.lblCurentSection.text() in ['Temperature Plot', 'Performance', 'Temperature Matrix Plot']:
                self._handle_temperature_plot_inclusion(plot_info)

            self.ui.lblLogInfo.setText(f"Plots for '{plot_info['title']}' added to report")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error including plot: {str(e)}")
            traceback.print_exc()

    def _get_current_plot_info(self):
        """Get current plot title and type"""
        try:
            current_plot = self.ui.lblCurentSection.text()

            if current_plot == 'Pressure Plot':
                if self.tab_widget.count() > 1:
                    if self.tab_widget.currentIndex() == 0:
                        title = self.axes_selected.title.get_text()
                    else:
                        title = self.axes.title.get_text()
                else:
                    title = self.axes.title.get_text()
            else:
                # Get title from current axes
                if hasattr(self, 'axes') and self.axes is not None:
                    title = self.axes.get_title()
                    if not title:  # If title is empty
                        title = "Max Temperature Distribution Across Locations" if current_plot == "Temperature Matrix Plot" else "Temperature Plot"
                else:
                    title = "Temperature Plot"

            return {
                'title': title,
                'type': 'pressure' if current_plot == 'Pressure Plot' else self.current_plot_type
            }
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error getting plot info: {str(e)}")
            traceback.print_exc()
            return None

    def _handle_pressure_plot_inclusion(self, plot_info):
        """Handle pressure plot inclusion logic"""
        try:
            plots_dir = self._create_plot_directory('pressure')

            # Only save the currently visible tab
            if self.current_tab_name == 'Full Range':
                self._save_single_plot(
                    self.current_figures.get('full_range'),
                    plots_dir,
                    plot_info['title'],
                    'Full Range',
                    'pressure'
                )
            elif self.current_tab_name == 'Selected Range':
                self._save_single_plot(
                    self.current_figures.get('selected_range'),
                    plots_dir,
                    plot_info['title'],
                    'Selected Range',
                    'pressure'
                )
        except Exception as e:
            raise Exception(f"Error in pressure plot inclusion: {str(e)}")

    def _handle_temperature_plot_inclusion(self, plot_info):
        """Handle temperature plot inclusion logic"""
        try:
            plots_dir = self._create_plot_directory('temperature')

            # Handle temperature matrix plot specifically
            if self.current_plot_type == 'temperature_matrix':
                if 'temperature_matrix' in self.current_figures:
                    self._save_single_plot(
                        self.current_figures['temperature_matrix'],
                        plots_dir,
                        plot_info['title'],
                        'matrix',
                        'temperature'
                    )
                    return

            # Handle other temperature plots
            for view_type, fig in self.current_figures.items():
                if fig is not None:
                    self._save_single_plot(
                        fig,
                        plots_dir,
                        plot_info['title'],
                        view_type,
                        self.current_plot_type
                    )
        except Exception as e:
            print(f"Error in temperature plot inclusion: {str(e)}")  # Added print for debugging
            raise Exception(f"Error in temperature plot inclusion: {str(e)}")

    def _create_plot_directory(self, plot_type):
        """Create and return plot directory path"""
        plots_dir = os.path.join(self.get_executable_path(), "temp_report", "plots", plot_type)
        os.makedirs(plots_dir, exist_ok=True)
        return plots_dir

    def _clone_figure_for_report(self, fig):
        """Clone a figure without applying tight_layout for report generation"""
        try:
            # Create a new figure with the same size
            new_fig = plt.figure(figsize=(10, 7), dpi=300)

            # Copy the content from the original figure
            for ax in fig.get_axes():
                # Get the position and add a new axes at the same position
                new_ax = new_fig.add_axes(ax.get_position().bounds)

                # Copy all lines
                for line in ax.get_lines():
                    if isinstance(line, plt.Line2D):
                        # Check if it's an axhline
                        if line.get_xdata()[0] == line.get_xdata()[-1]:
                            # This is likely an axhline
                            new_ax.axhline(y=line.get_ydata()[0],
                                           color=line.get_color(),
                                           linestyle=line.get_linestyle(),
                                           linewidth=line.get_linewidth(),
                                           label=line.get_label(),
                                           alpha=line.get_alpha())
                        else:
                            # Regular line plot
                            new_ax.plot(line.get_xdata(), line.get_ydata(),
                                        color=line.get_color(),
                                        linestyle=line.get_linestyle(),
                                        linewidth=line.get_linewidth(),
                                        marker=line.get_marker(),
                                        markersize=line.get_markersize(),
                                        label=line.get_label(),
                                        alpha=line.get_alpha())

                # Copy axis labels, title, and limits
                new_ax.set_xlabel(ax.get_xlabel())
                new_ax.set_ylabel(ax.get_ylabel())
                new_ax.set_title(ax.get_title())
                new_ax.set_xlim(ax.get_xlim())
                new_ax.set_ylim(ax.get_ylim())

                # Copy grid settings
                new_ax.grid(ax.get_grid())

                # Copy background transparency
                new_ax.patch.set_alpha(0.0)
                new_fig.patch.set_alpha(0.0)

                # Add legend if present with the same properties
                if ax.get_legend() is not None:
                    legend = ax.get_legend()
                    new_ax.legend(
                        bbox_to_anchor=legend.get_bbox_to_anchor(),
                        loc=legend._loc,
                        facecolor=legend.get_frame().get_facecolor(),
                        edgecolor=legend.get_frame().get_edgecolor(),
                        title=legend.get_title().get_text() if legend.get_title() else None
                    )

                # Copy text annotations
                for text in ax.texts:
                    new_ax.text(
                        text.get_position()[0],
                        text.get_position()[1],
                        text.get_text(),
                        fontsize=text.get_fontsize(),
                        color=text.get_color(),
                        alpha=text.get_alpha(),
                        rotation=text.get_rotation(),
                        horizontalalignment=text.get_horizontalalignment(),
                        verticalalignment=text.get_verticalalignment()
                    )

            # Set a reasonable minimum size
            new_fig.set_size_inches(8, 6)

            # Adjust the subplot parameters
            new_fig.subplots_adjust(
                left=0.15,
                right=0.95,
                bottom=0.15,
                top=0.9,
                wspace=0.2,
                hspace=0.2
            )

            return new_fig
        except Exception as e:
            print(f"Error cloning figure: {str(e)}")
            return fig  # Return original figure if cloning fails

    def _save_single_plot(self, fig, plots_dir, title, view_type, plot_type):
        """Save single plot and add to preview"""
        try:
            if fig is None:
                print("Figure is None, skipping save")  # Added debug print
                return

            # Create unique filename
            suffix = f"_{view_type}" if view_type else ""
            filename = f"{title.replace(' ', '_')}{suffix}.png"
            plot_path = os.path.join(plots_dir, filename)

            print(f"Saving plot to: {plot_path}")  # Added debug print

            # Clone the figure for report to avoid tight_layout distortion
            report_fig = self._clone_figure_for_report(fig)

            # Save the cloned plot without tight_layout
            report_fig.savefig(plot_path, bbox_inches='tight', dpi=300, facecolor='white')

            # Close the cloned figure to free memory
            plt.close(report_fig)

            # Create plot info
            display_title = f"{title} ({view_type})" if view_type else title
            plot_info = {
                'path': plot_path,
                'title': display_title,
                'type': plot_type
            }

            print(f"Created plot info: {plot_info}")  # Added debug print

            # Check if plot already exists
            existing_plots = [p for p in self.report_plots['custom']
                              if p['title'] == display_title and p['type'] == plot_type]

            if not existing_plots:
                # Add to custom plots list and preview only if it doesn't exist
                self.report_plots['custom'].append(plot_info)
                self.add_plot_to_preview(plot_info)

                QMessageBox.information(self, "Plot Inclusion Status",
                                        f"Plot '{display_title}' will be added to the report.")
                self.ui.lblLogInfo.setText(f"Plot '{display_title}' added to report")
            else:
                print(f"Plot already exists: {display_title}")  # Added debug print

        except Exception as e:
            print(f"Error in _save_single_plot: {str(e)}")  # Added debug print
            raise

    def add_plot_to_preview(self, plot_info):
        """Enhanced version of add_plot_to_preview with interactivity"""
        try:
            # Initialize layout for scroll area widget if it doesn't exist
            if not self.ui.scrollAreaWidgetContents.layout():
                preview_layout = QVBoxLayout()
                self.ui.scrollAreaWidgetContents.setLayout(preview_layout)

            # Create preview container with enhanced styling
            preview_widget = QWidget()
            container_layout = QVBoxLayout(preview_widget)
            container_layout.setSpacing(5)
            container_layout.setContentsMargins(5, 5, 5, 5)
            preview_widget.setStyleSheet("""
                QWidget {
                    background-color: #e8e9ea;
                    border-radius: 5px;
                    padding: 5px;
                    margin: 2px;
                }
                QWidget:hover {
                    background-color: #b5b6b6;
                }
            """)

            # Header layout for title and remove button
            header_widget = QWidget()
            header_layout = QHBoxLayout(header_widget)
            header_layout.setContentsMargins(1, 1, 1, 1)

            # Add title
            title_label = QLabel(plot_info['title'])
            title_label.setStyleSheet("""
                QLabel {
                    color: black;
                    font-size: 12px;
                    padding: 5px;
                    background-color: transparent;
                }
            """)
            header_layout.addWidget(title_label)

            # Add remove button
            remove_button = QPushButton("X")
            remove_button.setFixedSize(25, 25)
            remove_button.setStyleSheet("""
                QPushButton {
                    background-color: #994444;
                    border-radius: 5px;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #aa5555;
                }
            """)
            remove_button.clicked.connect(lambda: self.remove_plot_preview(plot_info))
            header_layout.addWidget(remove_button)

            container_layout.addWidget(header_widget)

            # Create small preview image
            preview_label = QLabel()
            preview_label.setStyleSheet("background-color: transparent;")
            small_pixmap = self.create_preview_pixmap(plot_info['path'], (234, 170))
            if small_pixmap:
                preview_label.setPixmap(small_pixmap)
                preview_label.setAlignment(Qt.AlignCenter)
                container_layout.addWidget(preview_label)

                # Make the entire widget clickable
                preview_widget.mousePressEvent = lambda e: self.show_large_plot(plot_info['path'])
                preview_widget.setCursor(Qt.PointingHandCursor)

                # Store widget reference in plot_info
                plot_info['widget'] = preview_widget

                # Add to scroll area with spacing
                self.ui.scrollAreaWidgetContents.layout().addWidget(preview_widget)
                self.ui.scrollAreaWidgetContents.layout().addSpacing(5)

                # Ensure scroll area updates
                self.ui.scrollAreaWidgetContents.updateGeometry()
                self.ui.scrlAreaReportPreview.viewport().update()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error adding plot preview: {str(e)}")
            traceback.print_exc()

    def remove_plot_preview(self, plot_info):
        """Remove a plot preview and its associated file from temp_report folder"""
        try:
            # Remove widget from UI
            if 'widget' in plot_info:
                widget = plot_info['widget']
                self.ui.scrollAreaWidgetContents.layout().removeWidget(widget)
                widget.deleteLater()

            # Remove plot file from temp_report folder
            if 'path' in plot_info and os.path.exists(plot_info['path']):
                try:
                    os.remove(plot_info['path'])
                except Exception as e:
                    print(f"Error removing plot file: {str(e)}")

                # Also remove the directory if it's empty
                plot_dir = os.path.dirname(plot_info['path'])
                if os.path.exists(plot_dir) and not os.listdir(plot_dir):
                    try:
                        os.rmdir(plot_dir)
                    except Exception as e:
                        print(f"Error removing empty directory: {str(e)}")

            # Remove from report plots
            for category in ['default', 'custom']:
                self.report_plots[category] = [
                    plot for plot in self.report_plots[category]
                    if plot['path'] != plot_info['path']
                ]

            self.ui.lblLogInfo.setText(f"Removed plot: {plot_info['title']}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error removing plot preview: {str(e)}")
            traceback.print_exc()

    def clear_plot_layout(self):
        """Clear all widgets from the plot layout"""
        try:
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                widget = item.widget()
                widget.setParent(None)
                widget.deleteLater()

            # Close all matplotlib figures
            plt.close('all')

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot layout: {str(e)}")
            traceback.print_exc()

    def clear_current_plot(self):
        """Clear the current plot and associated widgets"""
        try:
            # Clear the plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Close current figure if it exists
            if self.current_figure is not None:
                plt.close(self.current_figure)
                self.current_figure = None

            self.current_canvas = None
            self.current_toolbar = None
            if self.plot_container is not None:
                self.plot_container.deleteLater()
                self.plot_container = None

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot: {str(e)}")
            traceback.print_exc()

    def create_preview_pixmap(self, image_path, size=(200, 150)):
        """Create a scaled preview pixmap from an image file"""
        try:
            if not os.path.exists(image_path):
                return None

            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                return pixmap.scaled(
                    size[0], size[1],
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
            return None

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating preview: {str(e)}")
            return None

    def show_large_plot(self, image_path):
        """Show larger version of the plot in the plot frame"""
        try:
            # Switch to plots view
            self.ui.contentStack.setCurrentWidget(self.ui.plots)
            self.ui.plotTopBar.hide()

            # Clear existing plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    widget = item.widget()
                    widget.setParent(None)
                    widget.deleteLater()

            # Create label for large plot
            plot_label = QLabel()
            plot_label.setStyleSheet("background-color: transparent;")

            # Calculate size based on plot frame size
            available_width = self.ui.plotFrame.width() - 10
            available_height = self.ui.plotFrame.height() - 10

            # Load and scale the image
            pixmap = QPixmap(image_path)
            scaled_pixmap = pixmap.scaled(
                available_width,
                available_height,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            plot_label.setPixmap(scaled_pixmap)
            plot_label.setAlignment(Qt.AlignCenter)

            # Add to layout
            self.plot_layout.addWidget(plot_label)

            self.ui.lblLogInfo.setText("Plot displayed")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error showing large plot: {str(e)}")
            traceback.print_exc()

    def add_default_plots_to_preview(self):
        """Add default temperature analysis plots to preview"""
        try:
            plot_configs = [
                ('all_temperatures.png', 'All Temperatures'),
                # ('thruster_temperatures.png', 'Thruster Temperatures'),
                # ('tank_temperatures.png', 'Tank Temperatures'),
                # ('chamber_temperature.png', 'Chamber Temperature'),
                # ('max_temperatures.png', 'Maximum Temperatures')
            ]

            base_path = self.get_executable_path()
            plots_dir = os.path.join(base_path, "temp_report", "plots", "temperature")

            if os.path.exists(plots_dir):
                # Clear existing default plots
                self.report_plots['default'] = []

                for filename, title in plot_configs:
                    plot_path = os.path.join(plots_dir, filename)
                    if os.path.exists(plot_path):
                        plot_info = {
                            'path': plot_path,
                            'title': title,
                            'type': 'temperature'
                        }
                        self.report_plots['default'].append(plot_info)
                        self.add_plot_to_preview(plot_info)

                self.ui.lblLogInfo.setText("Added default plots to preview")

            # Only try to delete temp plots folder if db_handler exists
            if hasattr(self, 'db_handler') and self.db_handler is not None:
                self.db_handler.delete_temp_plots_folder()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error adding default plots: {str(e)}")
            traceback.print_exc()

    def clear_plot_previews(self):
        """Enhanced version of clear_plot_previews"""
        try:
            if self.ui.scrollAreaWidgetContents.layout():
                # Remove all widgets
                while self.ui.scrollAreaWidgetContents.layout().count():
                    item = self.ui.scrollAreaWidgetContents.layout().takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()

                # Reset plot tracking
                self.report_plots = {
                    'default': [],
                    'custom': []
                }

                self.ui.lblLogInfo.setText("Cleared all plot previews")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot previews: {str(e)}")
            traceback.print_exc()

    def calculate_performance(self):
        """Calculate performance parameters with range-based pressure calculations"""

        try:
            # Change the content window to the appropriate page
            self.ui.contentStack.setCurrentWidget(self.ui.performance)

            # Get input values from UI
            gamma = float(self.ui.lnEdtGamma.text())
            gas_constant = float(self.ui.lnEdtGasConst.text())

            # Get chamber temperature
            if self.filtered_temp_data is not None:
                self.temp_analyzer.analyze_temperature_data(self.filtered_temp_data, output_dir=None, save_plot=False,
                                                            plot_types=None)
                chamber_temp = self.temp_analyzer.max_temperature_data['value_kelvin']
                max_temp_location = self.temp_analyzer.max_temperature_data['location']
                max_temp_time = self.temp_analyzer.max_temperature_data['time']

                # Update chamber temperature label
                self.ui.subLnEdtChambTemp.setText(
                    f"{chamber_temp:.2f}K @ {max_temp_location.split('(')[0].strip()}"
                )
            else:
                raise ValueError("Temperature analysis has not been performed yet")

            # Get propellant masses
            initial_mass = self.ui.subLnEdtWghtOfPropBefTest.value()  # in grams
            unused_mass = self.ui.subLnEdtWghtOfPropAftTest.value()  # in grams

            # Get pressure ranges from UI
            try:
                vac_min = self.ui.lnEdtVacPressRangeMin.value()
                vac_max = self.ui.lnEdtVacPressRangeMax.value()
                vac_range = (vac_min, vac_max)
            except (ValueError, AttributeError):
                vac_range = None
                print("Using full range for vacuum pressure")

            try:
                chamb_min = self.ui.lnEdtChambPressRangeMin.value()
                chamb_max = self.ui.lnEdtChambPressRangeMax.value()
                chamb_range = (chamb_min, chamb_max)
            except (ValueError, AttributeError):
                chamb_range = None
                print("Using full range for chamber pressure")

            # Verify pressure data is available
            if self.pressure_data is None:
                raise ValueError("Pressure data not available")

            # Getting burn time from the filterered temperature data
            if self.temperature_data is not None and 'time' in self.temperature_data.columns:
                start_time = self.temperature_data['time'].iloc[0]
                end_time = self.temperature_data['time'].iloc[-1]
                burn_time = end_time - start_time
            else:
                raise ValueError("Temperature time data not available")

            # Initialize performance calculator
            performance_calc = Performance()
            performance_calc.set_properties(gas_constant, gamma)

            # Calculate parameters with range-based pressure
            results = performance_calc.calculate_performance_parameters(
                pressure_data=self.pressure_data,
                chamber_temp=chamber_temp,
                initial_mass=initial_mass,
                unused_mass=unused_mass,
                burn_time=burn_time,
                tank_press_range=chamb_range,
                vac_press_range=vac_range
            )

            # Format results
            formatted_results = performance_calc.format_performance_results(results)

            # Update UI with results
            self.ui.subLnEdtChambPressure.setText(f'{results["tank_pressure"]:.2f}mbar')
            self.ui.subLnEdtVacPressure.setText(f'{results["vacuum_pressure"]:.2f}mbar')
            self.ui.subLnEdtCharVelo.setText(formatted_results['characteristic_velocity'])
            self.ui.subLnEdtMassFlowRate.setText(formatted_results['mass_flow_rate'])
            self.ui.subLnEdtCoefOfThrust.setText(formatted_results['thrust_coefficient'])
            self.ui.subLnEdtThrust.setText(formatted_results['thrust'])
            self.ui.subLnEdtSpcImpulse.setText(formatted_results['specific_impulse'])
            self.ui.subLnEdtTotImpulse.setText(formatted_results['total_impulse'])
            self.ui.subLnEdtBurnTime.setText(formatted_results['burn_time'])

            # Create summary for the ranges used
            range_summary = "Pressure Ranges Used:\n"
            if vac_range:
                range_summary += f"Vacuum Pressure: {vac_min:.2f}s to {vac_max:.2f}s\n"
            else:
                range_summary += "Vacuum Pressure: Full range\n"
            if chamb_range:
                range_summary += f"Chamber Pressure: {chamb_min:.2f}s to {chamb_max:.2f}s"
            else:
                range_summary += "Chamber Pressure: Full range"

            # Store results
            self.performance_results = {
                'inputs': {
                    'gamma': gamma,
                    'gas_constant': gas_constant,
                    'chamber_temp': chamber_temp,
                    'initial_mass': initial_mass,
                    'unused_mass': unused_mass,
                    'vac_range': vac_range,
                    'chamb_range': chamb_range,
                    'burn_time': burn_time
                },
                'results': results
            }

            # Update log
            self.ui.lblLogInfo.setText("Performance parameters calculated successfully")

            # Show ranges used
            QMessageBox.information(self, "Calculation Summary", range_summary)

        except ValueError as e:
            QMessageBox.critical(self, "Input Error",
                                 f"Please check your input values: {str(e)}")
            self.ui.lblLogInfo.setText("Error: Invalid input values")

    def save_plot_for_report(self, fig, plot_type, title):
        """Save plot information for report generation"""
        try:
            if not hasattr(self, 'report_plots'):
                self.report_plots = []

            # Create a unique filename
            filename = f"{plot_type}plot.png"

            # Create plots directory if it doesn't exist
            plots_dir = "report_plots"
            os.makedirs(plots_dir, exist_ok=True)

            # Save the figure
            filepath = os.path.join(plots_dir, filename)
            fig.savefig(filepath, bbox_inches='tight', dpi=300)

            # Store plot information
            plot_info = {
                'type': plot_type,
                'title': title,
                'filepath': filepath
            }

            self.report_plots.append(plot_info)
            self.ui.lblLogInfo.setText(f"Plot saved for report: {title}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error saving plot for report: {str(e)}")

    def collect_form_data(self):
        """Collect all form data from UI inputs"""
        try:
            # Convert DataFrame to a serializable format
            filtered_temp_dict = None
            if self.filtered_temp_data is not None:
                filtered_temp_dict = {
                    'selected_columns': self.selected_cols,
                    'selected_ranges': self.selected_ranges
                }

            # Basic Information about the test
            if self.ui.lblTestNumber.text() == '':
                test_no = None
            else:
                test_no = self.ui.subLnEdtTestNo.value()

            test_date = self.ui.subLnEdtTestDate.text()

            basic_info = {
                'Aim': self.ui.subLnEdtAim.text(),
                'Propellant': self.ui.subLnEdtProp.text(),
                'Catalyst': self.ui.subLnEdtCat.text(),
                'Propellant_RI_Before_Test': self.ui.subLnEdtPropRI.text(),
                # 'firing_duration': self.ui.lblFiringDuration.text()
            }

            # System specifications
            system_specs = {
                'Chamber number': self.ui.subLnEdtChmbrNo.text(),
                'Chamber material': self.ui.subLnEdtChmbrMat.text(),
                'Chamber depth (mm)': self.ui.subLnEdtChmbrDept.text(),
                'Chamber internal diameter (mm)': self.ui.subLnEdtInternalChmbrDia.text(),
                'Chamber external diameter (mm)': self.ui.subLnEdtExternalChmbrDia.text(),
                'Nozzle throat dimension (mm)': self.ui.subLnEdtNozlThrtDime.text(),
                'Retainer plate orifice diameter (mm)': self.ui.subLnEdtRetainerPltOrfcDia.text(),
                'Mesh material': self.ui.subLnEdtMeshMat.text(),
                'Mesh size': self.ui.subLnEdtMeshSize.text()
            }

            # Propellant specifications
            propellant_specs = {
                'Type of Propellant': self.ui.subLnEdtTypeOfProp.text(),
                'Concentration before testing (%)': self.ui.subLnEdtConcBefTest.value(),
                'Stability (Old/New -MIL)': self.ui.subLnEdtStability.text(),
                'Weight_of_propellant_before the test (g)': self.ui.subLnEdtWghtOfPropBefTest.text(),
                'Weight_of_propellant_after the test (g)': self.ui.subLnEdtWghtOfPropAftTest.text()
            }

            # Catalyst Specifications
            catalyst_specs = {
                'Catalyst_type': self.ui.subLnEdtCatType.text(),
                'Catalyst_Grade/ Composition': self.ui.subLnEdtCatGrade.text(),
                'Catalyst_size (mm)': self.ui.subLnEdtCatSize.text(),
                'Weight_of_the_catalyst_before the test (g)': self.ui.subLnEdtCatWghtBefTest.text(),
                # 'Preheat_temperature (°C)': self.ui.subLnEdtPrehtTemp.text()
            }

            # Component Details
            component_details = [
                # 'Pressure_sensor_type': self.ui.subLnEdtPressSensType.text(),
                # 'Pressure_sensor_range (mbar)': self.ui.subLnEdtPressSensRange.text(),
                # 'Pressure_sensor_input_and_output': self.ui.subLnEdtPressSensIO.text(),
                {'Pressure_sensor_type': self.ui.Vac_Chamb_Pressure_Sensr_type_Input.text(),
                'Pressure_sensor_number_&_slope_equation': self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.text(),
                'Pressure_sensor_range': self.ui.Vac_Chamb_Pressure_Snsr_range_Input.text(),
                'Pressure_sensor_input_and_output': self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.text()},
                {'Pressure_sensor_type': self.ui.Prop_Tank_Pressure_Sensr_type_Input.text(),
                 'Pressure_sensor_number_&_slope_equation': self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.text(),
                 'Pressure_sensor_range': self.ui.Prop_Tank_Pressure_Snsr_range_Input.text(),
                 'Pressure_sensor_input_and_output': self.ui.Prop_Tank__Pressure_Snsr_IO_Input.text()},
                {'Pressure_sensor_type': self.ui.Thruster_Pressure_Sensr_type_Input.text(),
                 'Pressure_sensor_number_&_slope_equation': self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.text(),
                 'Pressure_sensor_range': self.ui.Thruster_Pressure_Snsr_range_Input.text(),
                 'Pressure_sensor_input_and_output': self.ui.Thruster_Pressure_Snsr_IO_Input.text()},
                {'Heater_type': self.ui.subLnEdtHtrType.text(),
                'Heater_input_power (W)': self.ui.subLnEdtHtrInpPower.text()}
            ]

            # Test Details
            test_details = {
                'Propellant_tank_heater_cut-off_temperature (°C)': self.ui.subLnEdtPropTnkHtrCtOfTemp.text(),
                'Propellant_tank_heater_reset_temperature (°C)': self.ui.subLnEdtPropTnkHtrRstTemp.text(),
                'Test_procedure': self.ui.subLblTestProcValue.toPlainText()
            }

            # Heater Info
            heater_info = {
                'Heater_type': self.ui.subLnEdtHtrType_2.text(),
                'Heater_input_Voltage': self.ui.subLnEdtHtrInpVoltage.value(),
                'Heater_input_Current': self.ui.subLnEdtHtrInpCurrent.value(),
                'Heater_input_Wattage': self.ui.subLnEdtHtrInpWattage.value(),
                'Heater_cut_off_temp (°C)': self.ui.subLnEdtHtrCtOfTemp.text(),
                'Heater_reset_temp (°C)' : self.ui.subLnEdtHtrRstTemp.text()
            }

            # Heater Cycles
            heater_cycles = []
            for cycle in range(1, 5):  # 4 cycles
                cycle_data = {
                    'switch_on': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOnTime').text(),
                    'switch_on_corresponding_tank_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtONCorspgTankPressure').text(),
                    'switch_on_corresponding_thruster_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtONCorspgThrusterPressure').text(),
                    'switch_off': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOffTime').text(),
                    'switch_off_corresponding_tank_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOFFCorspgTankPressure').text(),
                    'switch_off_corresponding_thruster_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOFFCorspgThrusterPressure').text(),
                    'max_temp': getattr(self.ui, f'cyc{cycle}SubLnEdtMaxTemp').text(),
                    'max_temp_location': getattr(self.ui, f'cyc{cycle}SubLnEdtLoc').text(),
                    'tank_bottom_temp': getattr(self.ui, f'cyc{cycle}SubLnEdtCorrespgTemp').text()
                }
                heater_cycles.append(cycle_data)

            # Note
            note = self.ui.subLnEdtNote.toPlainText()

            # Post Test Observations
            post_test_obs = {
                'Chamber_number': self.ui.subLnEdtChmbrNoPostTestObs.text(),
                'Chamber_length (mm)': self.ui.subLnEdtChmbrLen_2.text(),
                'Chamber_internal_diameter (mm)': self.ui.subLnEdtChmbrIntDia_2.text(),
                'Chamber_external_diameter (mm)': self.ui.subLnEdtChmbrExtDia_2.text(),
                'Mesh_condition': self.ui.subLnEdtMeshCond_2.text(),
                'Retainer_plate_condition': self.ui.subLnEdtRetainerPltCond_2.text(),
            }

            catalyst_post_analysis = {
                'catalyst_details/specification': self.ui.subLnEdtCatDet.text(),
                'catalyst_color_before': self.ui.subLnEdtCatColBfr.text(),
                'catalyst_color_after': self.ui.subLnEdtCatColAft.text(),
                'catalyst_weight_filled': self.ui.subLnEdtCatWghtFild.value(),
                'catalyst_weight_recovered': self.ui.subLnEdtCatWghtRecvrd.value(),
                'catalyst_change_percentage': self.ui.subLnEdtCatLosPerc.value()
            }

            propellant_post_analysis = {
                'Propellant_details/specification': self.ui.subLnEdtPropDet_2.text(),
                'Propellant_color_before': self.ui.subLnEdtPropColBef_2.text(),
                'Propellant_color_after': self.ui.subLnEdtPropColAft_2.text(),
                'Propellant_weight_filled (g)': self.ui.subLnEdtPropWghtFild_2.text(),
                'Propellant_weight_recovered (g)': self.ui.subLnEdtPropWghtRecvrd_2.text(),
                'Propellant_used_percentage (%)': self.ui.subLnEdtPropUsedPerc_2.text(),
                'Propellant_RI_(before_firing)': self.ui.subLnEdtPropRIBefFirg_2.text(),
                'Propellant_RI_(after_firing)': self.ui.subLnEdtPropRIAftFirg_2.text(),
                'Firing_duration (s)': self.ui.subLnEdtFirgDur_2.text(),
                'Approximate_mass_flow_rate (mg/s)': self.ui.subLnEdtApproxMassFlowRate_2.text(),
                "prop_conc_bef_table": self.ui.subLblPropBefConcTable.text(),
                "prop_conc_aft_table": self.ui.subLblPropAftConcTable.text(),
                "prop_ri_bef_table": self.ui.subLblPropBefRITable.text(),
                "prop_ri_aft_table": self.ui.subLblPropAftRITable.text()
            }

            system_performance = {
                'Chamber_pressure (mbar)': self.ui.subLnEdtChambPressure.text(),
                'Vacuum_chamber_pressure (mbar)': self.ui.subLnEdtVacPressure.text(),
                'Maximum_temperature (K)': self.ui.subLnEdtChambTemp.text(),
                'Characteristic_velocity (m/s)': self.ui.subLnEdtCharVelo.text(),
                'Coefficient_of_thrust': self.ui.subLnEdtCoefOfThrust.text(),
                'Burn_time (s)': self.ui.subLnEdtBurnTime.text(),
                'Mass_flow_rate (mg/s)': self.ui.subLnEdtMassFlowRate.text(),
                'Thrust (mN)': self.ui.subLnEdtThrust.text(),
                'Specific_impulse (s)': self.ui.subLnEdtSpcImpulse.text(),
                'Total_impulse (Ns)': self.ui.subLnEdtTotImpulse.text(),
                'Chamber_pressure_lower_limit (s)': self.ui.lnEdtChambPressRangeMin.value(),
                'Chamber_pressure_upper_limit (s)': self.ui.lnEdtChambPressRangeMax.value(),
                'Vacuum_pressure_lower_limit (s)': self.ui.lnEdtVacPressRangeMin.value(),
                'Vacuum_pressure_upper_limit (s)': self.ui.lnEdtVacPressRangeMax.value(),
                'filtered_data': filtered_temp_dict,
            }

            RI_Table = [
                ['Refractive Index', safe_float(self.ui.subLblPropBefRITable.text()), safe_float(self.ui.subLblPropAftRITable.text())],
                ['Concentration', safe_float(self.ui.subLblPropBefConcTable.text()), safe_float(self.ui.subLblPropAftConcTable.text())]
            ]

            test_authorization = {
                'Test Conducted by': self.ui.subLnEdtTestConductedBy.text(),
                'Report Generated by': self.ui.subLnEdtReportGeneratedBy.text(),
                'Report Authorized by': self.ui.subLnEdtReportAuthorizedBy.text()
            }

            pressure_relations = {
                'Vacuum_chamber_pressure_relation': self.ui.lnEdtY0PressureRelation.text(),
                'Propellant_tank_pressure_relation': self.ui.lnEdtY1PressureRelation.text(),
                'Thruster_chamber_pressure_relation': self.ui.lnEdtY2PressureRelation.text()
            }

            # Update test data dictionary
            self.test_data.update({
                'test_no': test_no,
                'test_date': test_date,
                'basic_info': basic_info,
                'system_specs': system_specs,
                'propellant_specs': propellant_specs,
                'catalyst_specs': catalyst_specs,
                'component_details': component_details,
                'test_details': test_details,
                'heater_info': heater_info,
                'heater_cycles': heater_cycles,
                'note': note,
                'post_test_observations': post_test_obs,
                'catalyst_post_analysis': catalyst_post_analysis,
                'propellant_post_analysis': propellant_post_analysis,
                'RI_table': RI_Table,
                'system_performance': system_performance,
                'test_authorization': test_authorization,
                'pressure_relations': pressure_relations
            })

            # Log success
            self.ui.lblLogInfo.setText("Form data collected successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error collecting form data: {str(e)}")
            self.ui.lblLogInfo.setText("Error collecting form data")
            raise

    def update_y_axis_options(self, plot_type: str):
        """Update Y-axis options based on selected X-axis"""
        try:
            if plot_type == 'temperature':
                selected_x = self.ui.comboBoxXAxisTemp.currentText()
                self.ui.comboBoxYAxisTemp.model.clear()
                columns = [col for col in self.temperature_data.columns if col != selected_x]
                self.ui.comboBoxYAxisTemp.addItems(columns)

            elif plot_type == 'pressure':
                selected_x = self.ui.comboBoxXAxisPressure.currentText()
                self.ui.comboBoxYAxisPressure.clear()
                columns = [col for col in self.pressure_data.columns if col != selected_x]
                self.ui.comboBoxYAxisPressure.addItems(columns)

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error updating Y-axis options: {str(e)}")

    def select_photo(self, photo_id: str):
        """Handle photo selection and preview for the specified photo widget"""
        try:
            widgets = self.photo_widgets.get(photo_id)
            if not widgets:
                raise ValueError(f"Invalid photo ID: {photo_id}")

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                f"Select {widgets['label']}",
                "",
                "Images (*.png *.jpg *.jpeg)"
            )

            if file_path:
                # Validate image using image handler
                is_valid, error = self.image_handler.validate_image(file_path)
                if is_valid:
                    # Update line edit with file path
                    widgets['line_edit'].setText(file_path)

                    # Create preview pixmap
                    preview_pixmap = QPixmap(file_path)
                    scaled_pixmap = preview_pixmap.scaled(
                        40, 40,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )

                    # Create a clickable label for the preview
                    preview_label = widgets['preview']
                    preview_label.setPixmap(scaled_pixmap)
                    preview_label.setCursor(Qt.PointingHandCursor)

                    # Store the file path for later use
                    preview_label.setProperty("image_path", file_path)

                    # Connect click handler if not already connected
                    try:
                        preview_label.disconnect()
                    except:
                        pass

                    preview_label.mousePressEvent = lambda e, pid=photo_id: self.show_full_photo(file_path, pid)

                    self.ui.lblLogInfo.setText(f"Selected photo for {widgets['label']}")
                else:
                    QMessageBox.warning(
                        self,
                        "Invalid Image",
                        f"Please select a valid image file. Error: {error}"
                    )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error selecting photo: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error selecting photo: {str(e)}")

    def show_full_photo(self, image_path, photo_id):
        try:
            if image_path and os.path.exists(image_path):
                dialog = PhotoPreviewDialog(image_path, photo_id, self)
                dialog.image_deleted.connect(self.handle_image_deleted)
                dialog.exec()
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error showing full photo: {str(e)}")

    def handle_image_deleted(self, photo_id):
        widgets = self.photo_widgets.get(photo_id)
        if widgets:
            widgets['line_edit'].setText('No Photo Selected')
            widgets['preview'].clear()
            widgets['preview'].setStyleSheet(u'background-color:#333333')
            # Reset the internal data if needed
            self.test_data.get('photo_paths', {}).pop(photo_id, None)

    def get_photo_paths(self) -> Dict[str, str]:
        """Get dictionary of photo paths for report generation"""
        return {
            photo_id: widgets['line_edit'].text().strip()
            for photo_id, widgets in self.photo_widgets.items()
            if widgets['line_edit'].text().strip()
        }

    def generate_report(self):
        """Generate and handle the report"""
        temp_dir = None
        try:
            # Collect form data
            self.collect_form_data()

            # Get photo paths
            photo_paths = self.get_photo_paths()

            # Font verification is now handled centrally by setup_fonts()

            # Create temporary directory
            temp_dir = tempfile.mkdtemp(prefix='vapr_idex_')
            plots_dir = os.path.join(temp_dir, 'plots')
            temp_plots_dir = os.path.join(plots_dir, 'temperature')
            pressure_plots_dir = os.path.join(plots_dir, 'pressure')

            # Create directories with proper permissions
            for directory in [temp_dir, plots_dir, temp_plots_dir, pressure_plots_dir]:
                os.makedirs(directory, exist_ok=True)
                os.chmod(directory, 0o777)

            # Prepare plot paths
            plot_paths = {}

            for category in ['default', 'custom']:
                for plot_info in self.report_plots[category]:
                    if os.path.exists(plot_info['path']):
                        dest_dir = temp_plots_dir if plot_info['type'] == 'temperature' else pressure_plots_dir
                        os.makedirs(dest_dir, exist_ok=True)
                        dest_path = os.path.join(dest_dir, os.path.basename(plot_info['path']))
                        shutil.copy2(plot_info['path'], dest_path)

            # Update plot_paths if directories contain files
            if os.path.exists(temp_plots_dir) and os.listdir(temp_plots_dir):
                plot_paths['temperature'] = temp_plots_dir
            if os.path.exists(pressure_plots_dir) and os.listdir(pressure_plots_dir):
                plot_paths['pressure'] = pressure_plots_dir

            # Generate PDF
            self.report_generator = TestReportGenerator(temp_dir)
            self.report_generator.test_data = self.test_data
            pdf_path = self.report_generator.generate_pdf(plot_paths, photo_paths)

            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"Generated PDF not found at {pdf_path}")

            self._current_plot_paths = plot_paths

            print(f"The plot path contains: {plot_paths}")

            # Show preview dialog
            preview_dialog = ReportPreviewDialog(pdf_path, self)
            if preview_dialog.exec() == QDialog.DialogCode.Accepted:
                # Get save location for PDF
                save_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save Report",
                    f"Test_Report_{self.test_data['test_no']}.pdf",
                    "PDF files (*.pdf)"
                )
                if save_path:
                    try:
                        if not save_path.lower().endswith('.pdf'):
                            save_path += '.pdf'
                        chosen_dir = os.path.dirname(save_path)
                        base_name = os.path.splitext(os.path.basename(save_path))[0]
                        final_pdf_path = os.path.join(chosen_dir, f"{base_name}.pdf")
                        shutil.copy2(pdf_path, final_pdf_path)
                        final_plots_dir = os.path.join(chosen_dir, 'plots')
                        if os.path.exists(final_plots_dir):
                            shutil.rmtree(final_plots_dir)
                        shutil.copytree(plots_dir, final_plots_dir)
                        json_path = self.save_json_file(chosen_dir, base_name, plots_dir=final_plots_dir)
                        QMessageBox.information(
                            self,
                            "Success",
                            f"Report and test data saved successfully:\nPDF: {final_pdf_path}\nJSON: {json_path}"
                        )
                        self._current_report_path = final_pdf_path
                        self._current_plots_dir = final_plots_dir
                        # Update plot paths to final locations
                        self._current_plot_paths = {
                            'temperature': os.path.join(final_plots_dir, 'temperature'),
                            'pressure': os.path.join(final_plots_dir, 'pressure')
                        }
                    except Exception as e:
                        QMessageBox.critical(self, "Error", f"Error saving report: {str(e)}")

        except Exception as e:
            error_msg = f"Error generating report: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            QMessageBox.critical(self, "Error", error_msg)

        finally:
            # Cleanup temporary directory
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def save_to_database(self):
        """Save test data and report to database"""
        try:
            # Collect form data
            self.collect_form_data()

            # Convert DataFrame to a serializable format
            if hasattr(self, 'filtered_temp_data') and self.filtered_temp_data is not None:
                try:
                    # Save the selection criteria using the actual columns from filtered_temp_data
                    filtered_temp_dict = {
                        'selected_columns': [col for col in self.filtered_temp_data.columns if col != 'time'],
                        'selected_ranges': self.selected_ranges if hasattr(self, 'selected_ranges') else []
                    }

                    # Also save the actual data in a format that can be directly loaded
                    time_data = self.filtered_temp_data['time'].tolist()
                    temp_cols = [col for col in self.filtered_temp_data.columns if col != 'time']
                    temperature_data = {col: self.filtered_temp_data[col].tolist() for col in temp_cols}

                    # Combine both formats for maximum compatibility
                    filtered_temp_dict.update({
                        'time_data': time_data,
                        'temperature_data': temperature_data
                    })

                    # Add to performance_data
                    if 'performance_data' not in self.test_data:
                        self.test_data['performance_data'] = {}
                    self.test_data['performance_data']['filtered_temp_data'] = filtered_temp_dict

                    # Make sure we're not using the old key name
                    if 'filtered_temperature_data' in self.test_data['performance_data']:
                        del self.test_data['performance_data']['filtered_temperature_data']

                    print("Successfully prepared filtered temperature data for database storage")
                except Exception as e:
                    print(f"Error preparing filtered temperature data: {str(e)}")
                    import traceback
                    traceback.print_exc()

            # Check if test number is valid
            test_no = self.test_data.get('test_no') if hasattr(self, 'test_data') else None
            if not test_no or str(test_no).strip() == '':
                QMessageBox.warning(
                    self,
                    "Invalid Test Number",
                    "Please enter a valid test number before saving to database."
                )
                return

            # Check if a report has been generated
            if not hasattr(self, '_current_report_path') or not os.path.exists(self._current_report_path):
                QMessageBox.warning(self, "Warning", "Please generate and save a report first.")
                return

            # Check if plot paths are available
            if not hasattr(self, '_current_plot_paths'):
                QMessageBox.warning(self, "Warning", "Plot paths are not available. Please generate the report first.")
                return

            if not all(os.path.exists(path) for path in self._current_plot_paths.values()):
                QMessageBox.warning(self, "Warning",
                                    "One or more plot directories are missing. Cannot save to database.")
                return

            # Check if database handler is initialized
            if not hasattr(self, 'db_handler') or self.db_handler is None:
                QMessageBox.warning(
                    self,
                    "Database Not Connected",
                    "Please connect to the database first by entering the database password."
                )
                return

            # Verify database connection
            test_params = DatabaseConfig.get_connection_params(is_server=True)
            success, message = DatabaseConfig.test_connection(test_params)
            if not success:
                QMessageBox.critical(
                    self,
                    "Database Error",
                    f"Not connected to database: {message}\nPlease check your connection and try again."
                )
                return

            # Ensure test_no is an integer
            try:
                self.test_data['test_no'] = int(test_no)
            except (ValueError, TypeError):
                QMessageBox.warning(
                    self,
                    "Invalid Test Number",
                    "Test number must be a valid integer."
                )
                return

            test_auth_data = self._prepare_test_authorization_data()
            self.test_data.update({'test_authorization': test_auth_data})

            # Include temperature analysis if available
            if hasattr(self, 'temp_analyzer') and 'matrix' in self.temp_analyzer.analysis_results:
                self.test_data['temperature_analysis'] = self.temp_analyzer.analysis_results['matrix']

            current_photo_paths = {
                'prop_before': self.ui.subLnEdtPropPhtoBfr_2.text(),
                'prop_after': self.ui.subLnEdtPropPhtoAft_2.text(),
                'cat_before': self.ui.subLnEdtCatPhtoBfr_2.text(),
                'cat_after': self.ui.subLnEdtCatPhtoAft_2.text()
            }

            # Save data to database, including plot paths
            test_id = self.db_handler.save_test_data(self.test_data, self._current_plot_paths, current_photo_paths)
            if not test_id:
                raise Exception("Failed to save test data to database")

            # Save additional data if available
            if hasattr(self, 'temperature_data') and self.temperature_data is not None:
                self.db_handler.save_temperature_data(test_id, self.temperature_data)
            if hasattr(self, 'pressure_data') and self.pressure_data is not None:
                self.db_handler.save_pressure_data(test_id, self.pressure_data)

            # Save the report to the database
            self.db_handler.save_report(test_id, self._current_report_path)

            QMessageBox.information(
                self,
                "Success",
                "Data successfully saved to database!"
            )

            # Clean up temporary files
            self._cleanup_temp_directory()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Database Error",
                f"Error saving to database: {str(e)}"
            )

    def _prepare_test_authorization_data(self):
        """Prepare test authorization data for database storage"""
        try:
            return {
                'conducted_by': self.ui.subLnEdtTestConductedBy.text(),
                'generated_by': self.ui.subLnEdtReportGeneratedBy.text(),
                'authorized_by': self.ui.subLnEdtReportAuthorizedBy.text()
            }
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error preparing test authorization data: {str(e)}")
            return {}

    def _cleanup_temp_directory(self):
        """Clean up temporary directory after successful save"""
        try:
            if hasattr(self, '_temp_dir_to_cleanup') and self._temp_dir_to_cleanup:
                if os.path.exists(self._temp_dir_to_cleanup):
                    shutil.rmtree(self._temp_dir_to_cleanup)
                    self._temp_dir_to_cleanup = None
        except Exception as e:
            print(f"Error cleaning up temporary directory: {str(e)}")

    def save_json_file(self, base_dir, base_name, plots_dir=None):
        """Saves all test data fields entered by the user and associated plots in a JSON file."""
        json_file_path = os.path.join(base_dir, f"{base_name}.json")
        try:
            # Convert DataFrame to a serializable format
            if hasattr(self, 'filtered_temp_data') and self.filtered_temp_data is not None:
                # Save the selection criteria using the actual columns from filtered_temp_data
                self.filtered_temp_dict = {
                    'selected_columns': [col for col in self.filtered_temp_data.columns if col != 'time'],
                    'selected_ranges': self.selected_ranges if hasattr(self, 'selected_ranges') else []
                }

                # Also save the actual data in a format that can be directly loaded
                time_data = self.filtered_temp_data['time'].tolist()
                temp_cols = [col for col in self.filtered_temp_data.columns if col != 'time']
                temperature_data = {col: self.filtered_temp_data[col].tolist() for col in temp_cols}

                # Combine both formats for maximum compatibility
                self.filtered_temp_dict.update({
                    'time_data': time_data,
                    'temperature_data': temperature_data
                })

            # Collect test data
            test_data = self.auto_saver.get_all_test_data()

            # Add filtered temperature selection data if available
            if hasattr(self, 'filtered_temp_dict') and self.filtered_temp_dict is not None:
                test_data['filtered_temperature'] = self.filtered_temp_dict

            # Add firing duration if available
            if hasattr(self, 'temperature_data') and self.temperature_data is not None:
                firing_duration = self.temperature_data['time'].iloc[-1]
                test_data['firing_duration'] = firing_duration

            # Add temperature analysis if available
            if hasattr(self, 'temp_analyzer') and hasattr(self.temp_analyzer, 'analysis_results'):
                if 'matrix' in self.temp_analyzer.analysis_results:
                    analysis_df = self.temp_analyzer.analysis_results['matrix']
                    test_data['temperature_analysis'] = analysis_df.to_dict(orient='index')

            # Save temperature data if available
            if hasattr(self, 'temperature_data') and self.temperature_data is not None:
                test_data['temperature_data'] = {
                    'time': self.temperature_data['time'].tolist(),
                    'temperatures': {
                        col: self.temperature_data[col].tolist()
                        for col in self.temperature_data.columns if col != 'time'
                    }
                }

            # Save pressure data if available
            if hasattr(self, 'pressure_data') and self.pressure_data is not None:
                time_col = next((col for col in self.pressure_data.columns if 'time' in col.lower()), None)
                if time_col:
                    test_data['pressure_data'] = {
                        'time': self.pressure_data[time_col].tolist(),
                        'pressures': {
                            col: self.pressure_data[col].tolist()
                            for col in self.pressure_data.columns if col != time_col
                        }
                    }

            # Handle photos
            photos_metadata = {}
            photos_dir = os.path.join(base_dir, 'photos')
            os.makedirs(photos_dir, exist_ok=True)

            # Process each photo type
            for photo_type, widget_info in self.photo_widgets.items():
                photo_path = widget_info['line_edit'].text()
                if photo_path and os.path.exists(photo_path):
                    # Create a standardized filename
                    new_filename = f"{photo_type}_{base_name}.png"
                    new_photo_path = os.path.join(photos_dir, new_filename)

                    # Copy the photo file
                    shutil.copy2(photo_path, new_photo_path)

                    # Store relative path in metadata
                    rel_path = os.path.join('photos', new_filename)
                    photos_metadata[photo_type] = {
                        'relative_path': rel_path,
                        'label': widget_info['label']
                    }

            # Add photos metadata to test data
            test_data['photos'] = photos_metadata

            # Handle plots
            plot_metadata = {'default': [], 'custom': []}
            if plots_dir:
                # Use existing plots_dir with temperature and pressure subfolders
                temp_dir = os.path.join(plots_dir, 'temperature')
                press_dir = os.path.join(plots_dir, 'pressure')
                for category in ['default', 'custom']:
                    for plot_info in self.report_plots[category]:
                        sub_dir = 'temperature' if plot_info['type'] == 'temperature' else 'pressure'
                        plot_filename = os.path.basename(plot_info['path'])
                        rel_path = os.path.join('plots', sub_dir, plot_filename)
                        full_path = os.path.join(plots_dir, sub_dir, plot_filename)
                        if os.path.exists(full_path):
                            plot_metadata[category].append({
                                'relative_path': rel_path,
                                'title': plot_info['title'],
                                'type': plot_info['type']
                            })
                        else:
                            print(f"Warning: Plot file not found at {full_path}")
            else:
                # Original behavior: create a new plots folder
                new_plots_dir = os.path.join(base_dir, f"{base_name}_plots")
                os.makedirs(new_plots_dir, exist_ok=True)
                for category in ['default', 'custom']:
                    for plot_info in self.report_plots[category]:
                        if 'path' in plot_info and os.path.exists(plot_info['path']):
                            plot_filename = f"{plot_info['title'].replace(' ', '_')}_{category}.png"
                            new_plot_path = os.path.join(new_plots_dir, plot_filename)
                            shutil.copy2(plot_info['path'], new_plot_path)
                            rel_path = os.path.join(f"{base_name}_plots", plot_filename)
                            plot_metadata[category].append({
                                'relative_path': rel_path,
                                'title': plot_info['title'],
                                'type': plot_info['type']
                            })

            # Add plot metadata to test data
            test_data['plots'] = plot_metadata

            # Save to JSON
            with open(json_file_path, 'w') as f:
                json.dump(test_data, f, indent=4)

            return json_file_path

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving test data: {str(e)}")
            print(f"Detailed error: {traceback.format_exc()}")
            return None

    def load_data_from_json_file(self):
        """Loads all the saved data field and plots in the GUI from a json file"""
        try:
            # Helper function to safely convert to float
            def safe_float(value, default=0.0):
                try:
                    if value and str(value).strip():
                        return float(value)
                    return default
                except (ValueError, TypeError):
                    return default

            json_file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Load Test Data JSON File",
                "",
                "JSON Files (*.json)"
            )

            if not json_file_path:
                return

            # Close the existing data dialog if it's open
            if hasattr(self, 'existing_data_dialog'):
                self.existing_data_dialog.accept()

            # Clear existing plot previews
            self.clear_plot_previews()

            # Initialize report_plots
            if not hasattr(self, 'report_plots'):
                self.report_plots = {'default': [], 'custom': []}

            # Load and parse JSON data
            with open(json_file_path, 'r') as f:
                data = json.load(f)

            base_dir = os.path.dirname(json_file_path)

            # Load firing duration if available
            if 'performance_data' in data:
                firing_duration = data['performance_data']["Burn_time (s)"]
                self.ui.lblFiringDuration.setText(firing_duration)
                self.ui.subLnEdtFirgDur_2.setValue(safe_float(firing_duration.split(' ')[0]))

            # Load photos if available
            if 'photos' in data:
                for photo_type, photo_info in data['photos'].items():
                    if photo_type in self.photo_widgets:
                        rel_path = photo_info.get('relative_path')
                        if rel_path:
                            full_path = os.path.join(base_dir, rel_path)
                            if os.path.exists(full_path):
                                # Update the line edit with the path
                                self.photo_widgets[photo_type]['line_edit'].setText(full_path)
                                # Update the preview
                                self.update_photo_preview(
                                    self.photo_widgets[photo_type]['preview'],
                                    full_path
                                )

            # Load form data
            self.auto_saver.load_data_from_json(json_file_path)

            # Display temperature analysis if loaded
            if 'temperature_analysis' in data:
                df = pd.DataFrame.from_dict(data['temperature_analysis'], orient='index')
                self.temp_analyzer.analysis_results = {'matrix': df}
                self.display_temperature_analysis(df)

            # Load temperature data if available
            if 'temperature_data' in data:
                temp_data = data['temperature_data']
                df_data = {'time': temp_data['time']}
                df_data.update(temp_data['temperatures'])
                self.temperature_data = pd.DataFrame(df_data)

                # Update UI indicators
                self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempMatrix.setEnabled(True)
                self.ui.btnPlots.setEnabled(True)

            # Load pressure data if available
            if 'pressure_data' in data:
                pressure_data = data['pressure_data']
                df_data = {'time': pressure_data['time']}
                df_data.update(pressure_data['pressures'])
                self.pressure_data = pd.DataFrame(df_data)

                # Update UI indicators
                self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')

                # Enable the plots button
                self.ui.btnPlots.setEnabled(True)

            # Load filter data
            filtered_data_dict = data.get('filtered_temperature', {})
            if filtered_data_dict:
                # Check if we have the direct data format
                if 'time_data' in filtered_data_dict and 'temperature_data' in filtered_data_dict:
                    # Create DataFrame directly from the saved data
                    time_data = filtered_data_dict['time_data']
                    temperature_data = filtered_data_dict['temperature_data']

                    df_data = {'time': time_data}
                    for col, values in temperature_data.items():
                        df_data[col] = values

                    self.filtered_temp_data = pd.DataFrame(df_data)

                    # Also set selected columns and ranges for future use
                    self.selected_cols = filtered_data_dict.get('selected_columns', list(temperature_data.keys()))
                    self.selected_ranges = filtered_data_dict.get('selected_ranges', [])

                    # Setting indicator color green
                    self.ui.tempMatrixIndicator.setStyleSheet("""
                        background-color: rgb(6, 196, 142);
                        border-radius: 7px;
                    """)
                else:
                    # Use the old method of reconstructing from selection criteria
                    self.selected_cols = filtered_data_dict.get('selected_columns', [])
                    self.selected_ranges = filtered_data_dict.get('selected_ranges', [])

                    if self.selected_cols and self.selected_ranges is not None and hasattr(self, 'temperature_data') and self.temperature_data is not None:
                        # Create mask for selected ranges
                        mask = pd.Series(False, index=self.temperature_data.index)

                        for start, end in self.selected_ranges:
                            mask |= ((self.temperature_data['time'] >= start) &
                                     (self.temperature_data['time'] <= end))

                        # Filter data
                        filtered_data = self.temperature_data[mask]

                        # Select only chosen columns plus time
                        columns_to_use = ['time'] + self.selected_cols
                        filtered_data = filtered_data[columns_to_use]

                        self.filtered_temp_data = filtered_data

                        # Setting indicator color green
                        self.ui.tempMatrixIndicator.setStyleSheet("""
                            background-color: rgb(6, 196, 142);
                            border-radius: 7px;
                        """)

            # Enable plots button if either data is loaded
            if hasattr(self, 'temperature_data') or hasattr(self, 'pressure_data'):
                self.ui.btnPlots.setEnabled(True)

            # Show top bar labels
            self.ui.lblAim.setVisible(True)
            self.ui.lblPropellant.setVisible(True)
            self.ui.lblCatalyst.setVisible(True)
            self.ui.testNoFrame.setVisible(True)

            self.ui.lblAim.setText(self.ui.subLnEdtAim.text())
            self.ui.lblPropellant.setText(self.ui.subLnEdtProp.text())
            self.ui.lblCatalyst.setText(self.ui.subLnEdtCat.text())
            self.ui.lblTestNumber.setText(str(self.ui.subLnEdtTestNo.value()))

            # Reset report plots dictionary
            self.report_plots = {'default': [], 'custom': []}

            # Load plots if they exist in the data
            if 'plots' in data:
                base_dir = os.path.dirname(json_file_path)

                for category in ['default', 'custom']:
                    if category in data['plots']:
                        for plot_info in data['plots'][category]:
                            try:
                                # Try absolute path first
                                plot_path = plot_info.get('absolute_path')

                                # If absolute path doesn't exist, try relative path
                                if not plot_path or not os.path.exists(plot_path):
                                    rel_path = plot_info.get('relative_path')
                                    if rel_path:
                                        plot_path = os.path.join(base_dir, rel_path)

                                if plot_path and os.path.exists(plot_path):
                                    plot_data = {
                                        'path': plot_path,
                                        'title': plot_info.get('title', 'Unnamed Plot'),
                                        'type': plot_info.get('type', 'temperature')
                                    }

                                    self.report_plots[category].append(plot_data)
                                    self.add_plot_to_preview(plot_data)
                                    print(f"Successfully loaded plot: {plot_data['title']}")
                                else:
                                    print(f"Plot file not found: {plot_path}")

                            except Exception as e:
                                print(f"Error loading plot: {str(e)}")
                                import traceback
                                traceback.print_exc()

            # Force enable the plots button
            self.ui.btnPlots.setEnabled(True)
            self.setup_plot_controls()

            print(f"Plots button directly enabled in load_data_from_json_file: {self.ui.btnPlots.isEnabled()}")

            # Call check_and_enable_plots_button to ensure the plots button is enabled
            if hasattr(self, 'auto_saver'):
                self.auto_saver.check_and_enable_plots_button()

            # Force enable again after a short delay using a timer
            QTimer.singleShot(500, lambda: self.force_enable_plots_button())

            # Set up multiple timers to keep trying to enable the button
            for delay in [1000, 2000, 3000, 4000, 5000]:
                QTimer.singleShot(delay, lambda: self.force_enable_plots_button())

            QMessageBox.information(
                self,
                "Success",
                "Test data and plots loaded successfully!"
            )

        except FileNotFoundError:
            QMessageBox.warning(
                self,
                "Error",
                "The specified JSON file or associated plot files could not be found."
            )
        except json.JSONDecodeError:
            QMessageBox.warning(
                self,
                "Error",
                "The JSON file is invalid or corrupted."
            )
        except Exception as e:
            import traceback
            QMessageBox.critical(
                self,
                "Error",
                f"Error loading test data: {str(e)}"
            )
            print(f"Detailed error: {traceback.format_exc()}")  # For debugging

    def update_photo_preview(self, preview_label, photo_path):
        """Update the photo preview in the UI"""
        try:
            pixmap = QPixmap(photo_path)
            if not pixmap.isNull():
                # Scale the pixmap to fit the label while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(
                    preview_label.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                preview_label.setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"Error updating photo preview: {str(e)}")

    def load_test_data(self):
        """Load test data from database."""
        try:
            # Close the initial dialog box
            self.existing_data_dialog.accept()

            # Clear existing plot previews
            self.clear_plot_previews()

            # Get all test numbers
            test_numbers = self.db_handler.get_all_test_numbers()
            if not test_numbers:
                QMessageBox.information(self, "Info", "No test data found in database.")
                return

            # Creating a dialog for test number selection
            test_no, ok = QInputDialog.getItem(
                self,
                "Load Test Data",
                "Select Test Number:",
                [str(num) for num in test_numbers],  # Convert to strings for display
                0,
                False
            )

            if ok and test_no:
                # Load test data
                test_data = self.db_handler.get_test_data(str(test_no))  # Convert back to string for DB query
                if test_data:
                    # Clear existing data
                    self.temperature_data = None
                    self.pressure_data = None
                    self.test_data = {}

                    # Update UI
                    self.update_ui_with_test_data(test_data)

                    # Show top bar labels
                    self.ui.lblAim.setVisible(True)
                    self.ui.lblPropellant.setVisible(True)
                    self.ui.lblCatalyst.setVisible(True)
                    self.ui.testNoFrame.setVisible(True)

                    self.filtered_temp_data = None

                    # Load temperature data
                    temp_data = self.db_handler.get_temperature_data(test_data['test_id'])
                    if temp_data is not None:
                        self.temperature_data = temp_data
                        self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                        self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                        self.ui.btnPlots.setEnabled(True)
                        self.ui.btnTempMatrix.setEnabled(True)
                        self.setup_plot_controls()

                    # Load pressure data
                    press_data = self.db_handler.get_pressure_data(test_data['test_id'])
                    if press_data is not None:
                        self.pressure_data = press_data
                        self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                        self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                        self.ui.btnPlots.setEnabled(True)

                    # Load filter data
                    if 'performance_data' in test_data:
                        try:
                            # Try to get filtered temperature data directly from the database
                            filtered_temp_data = self.db_handler.get_filtered_temp_data_from_performance(test_data['test_id'])
                            if filtered_temp_data is not None:
                                self.filtered_temp_data = filtered_temp_data

                                # Also set selected columns and ranges for future use
                                self.selected_cols = [col for col in filtered_temp_data.columns if col != 'time']

                                # Try to get selected ranges from performance data
                                filtered_data_dict = None
                                if 'filtered_temp_data' in test_data['performance_data']:
                                    filtered_data_dict = test_data['performance_data']['filtered_temp_data']
                                elif 'filtered_temperature_data' in test_data['performance_data']:
                                    filtered_data_dict = test_data['performance_data']['filtered_temperature_data']

                                if isinstance(filtered_data_dict, dict) and 'selected_ranges' in filtered_data_dict:
                                    self.selected_ranges = filtered_data_dict['selected_ranges']
                                else:
                                    # If we don't have ranges, try to infer them from the data
                                    self.selected_ranges = [(filtered_temp_data['time'].min(), filtered_temp_data['time'].max())]

                                # Setting indicator color green
                                self.ui.tempMatrixIndicator.setStyleSheet("""
                                    background-color: rgb(6, 196, 142);
                                    border-radius: 7px;
                                """)

                                print("Successfully loaded filtered temperature data")
                            else:
                                print("Failed to load filtered temperature data from database")
                        except Exception as e:
                            print(f"Error loading filtered temperature data: {str(e)}")
                            import traceback
                            traceback.print_exc()

                    # Load and display plots
                    test_plots = self.db_handler.get_test_plots(test_data['test_id'], self.add_plot_to_preview)
                    if test_plots:
                        self.report_plots = {
                            'default': [],
                            'custom': test_plots
                        }

                    # Load and display photos
                    test_data = self.db_handler.load_test_data(test_data['test_id'])
                    if test_data and 'photos' in test_data:
                        photos = test_data['photos']
                        # Update UI with photos
                        for photo_type, photo_path in photos.items():
                            # Update the corresponding photo widget
                            self.update_photo_widget(photo_type, photo_path)

                    # Update firing duration in UI if available
                    if 'performance_data' in test_data:
                        firing_duration = test_data['performance_data']["Burn_time (s)"]
                        self.ui.lblFiringDuration.setText(firing_duration)
                        # Safe float conversion for firing duration
                        try:
                            duration_value = float(firing_duration.split(' ')[0])
                            self.ui.subLnEdtFirgDur_2.setValue(duration_value)
                        except (ValueError, TypeError, IndexError):
                            self.ui.subLnEdtFirgDur_2.setValue(0.0)

                    # Display temperature analysis if available
                    if 'temperature_analysis' in test_data and test_data['temperature_analysis'] is not None:
                        self.temp_analyzer.analysis_results = {'matrix': test_data['temperature_analysis']}
                        self.display_temperature_analysis(test_data['temperature_analysis'])

                    # Update chamber and vacuum pressure ranges
                    if 'Vacuum_pressure_lower_limit (s)' in test_data and test_data['Vacuum_pressure_lower_limit (s)'] is not None:
                        self.ui.lnEdtVacPressRangeMin.setValue(test_data['Vacuum_pressure_lower_limit (s)'])
                        self.ui.lnEdtVacPressRangeMax.setValue(test_data['Vacuum_pressure_upper_limit (s)'])
                        self.ui.lnEdtChambPressRangeMin.setValue(test_data['Chamber_pressure_lower_limit (s)'])
                        self.ui.lnEdtChambPressRangeMax.setValue(test_data['Chamber_pressure_upper_limit (s)'])

                    QMessageBox.information(self, "Success", "Test data loaded successfully!")

                else:
                    QMessageBox.warning(self, "Warning", f"Failed to load data for test {test_no}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading test data: {str(e)}")
            traceback.print_exc()

    def update_photo_widget(self, photo_type: str, photo_path: str):
        """
        Update a photo widget with the loaded photo from database.

        Args:
            photo_type (str): Type of photo ('prop_before', 'prop_after', 'cat_before', 'cat_after')
            photo_path (str): Path to the photo file
        """
        try:
            # Get the widget info from photo_widgets dictionary
            widget_info = self.photo_widgets.get(photo_type)
            if not widget_info:
                raise ValueError(f"Invalid photo type: {photo_type}")

            if os.path.exists(photo_path):
                # Update line edit with file path
                widget_info['line_edit'].setText(photo_path)

                # Create preview pixmap
                preview_pixmap = QPixmap(photo_path)
                scaled_pixmap = preview_pixmap.scaled(
                    40, 40,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                # Update preview label
                preview_label = widget_info['preview']
                preview_label.setPixmap(scaled_pixmap)
                preview_label.setCursor(Qt.PointingHandCursor)
                preview_label.setProperty("image_path", photo_path)

                # Disconnect any existing click handlers
                try:
                    preview_label.disconnect()
                except:
                    pass

                # Connect new click handler
                preview_label.mousePressEvent = lambda e, pid=photo_type: self.show_full_photo(photo_path, pid)

                self.ui.lblLogInfo.setText(f"Loaded photo for {widget_info['label']}")
            else:
                # Reset widget if photo doesn't exist
                widget_info['line_edit'].setText('No Photo Selected')
                widget_info['preview'].clear()
                widget_info['preview'].setStyleSheet(u'background-color:#333333')
                self.ui.lblLogInfo.setText(f"Photo file not found: {photo_path}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error updating photo widget: {str(e)}")
            print(f"Error updating photo widget: {str(e)}")

    def update_ui_with_test_data(self, test_data):
        """Update UI elements with loaded test data."""
        try:
            # Safe float conversion with default values if conversion fails
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value != '' else default
                except (ValueError, TypeError):
                    return default

            # Basic Info
            basic_info = test_data.get('basic_info', {})


            # Handle test number
            test_no = test_data.get('test_no')
            if test_no is not None and str(test_no).strip():
                self.ui.subLnEdtTestNo.setValue(int(test_no))

            # Handle text fields with empty string fallback
            self.ui.subLnEdtAim.setText(basic_info.get('Aim', ''))
            self.ui.subLnEdtProp.setText(basic_info.get('Propellant', ''))
            self.ui.subLnEdtCat.setText(basic_info.get('Catalyst', ''))

            # Handle numeric fields with validation
            prop_ri = basic_info.get('Propellant_RI_Before_Test', '')
            if prop_ri and str(prop_ri).strip():
                try:
                    self.ui.subLnEdtPropRI.setValue(safe_float(prop_ri))
                except (ValueError, TypeError):
                    self.ui.subLnEdtPropRI.setValue(0.0)

            # Handle date
            test_date = test_data.get('test_date')
            if test_date:
                self.ui.subLnEdtTestDate.setDate(test_date)

            # Handle system performance data
            sys_perf_data = test_data.get('system_performance', {})
            self.ui.subLnEdtSpcImpulse.setText(str(sys_perf_data.get('Specific_impulse (s)', '')))
            self.ui.subLnEdtTotImpulse.setText(str(sys_perf_data.get('Total_impulse (Ns)', '')))
            # Update firing duration widget
            firing_duration = sys_perf_data.get('Burn_time (s)')
            if firing_duration is not None:
                self.ui.lblFiringDuration.setText(str(firing_duration))

            # Update test Authorization with empty string fallbacks
            test_authorization = test_data.get('test_authorization', {})
            self.ui.subLnEdtTestConductedBy.setText(test_authorization.get('Test Conducted by', ''))
            self.ui.subLnEdtReportGeneratedBy.setText(test_authorization.get('Report Generated by', ''))
            self.ui.subLnEdtReportAuthorizedBy.setText(test_authorization.get('Report Authorized by', ''))

            # Top bar
            self.ui.lblAim.setText(basic_info.get('Aim', ''))
            self.ui.lblPropellant.setText(basic_info.get('Propellant', ''))
            self.ui.lblCatalyst.setText(basic_info.get('Catalyst', ''))
            self.ui.lblTestNumber.setText(str(test_data.get('test_no', '')))

            # System Specs
            sys_specs = test_data.get('system_specs', {})
            self.ui.subLnEdtChmbrNo.setText(sys_specs.get('Chamber number', ''))
            self.ui.subLnEdtChmbrMat.setText(sys_specs.get('Chamber material', ''))
            self.ui.subLnEdtChmbrDept.setValue(safe_float(sys_specs.get('Chamber depth (mm)', '')))
            self.ui.subLnEdtInternalChmbrDia.setValue(safe_float(sys_specs.get('Chamber internal diameter (mm)', '')))
            self.ui.subLnEdtExternalChmbrDia.setValue(safe_float(sys_specs.get('Chamber external diameter (mm)', '')))
            self.ui.subLnEdtNozlThrtDime.setValue(safe_float(sys_specs.get('Nozzle throat dimension (mm)', '')))
            self.ui.subLnEdtRetainerPltOrfcDia.setValue(
                safe_float(sys_specs.get('Retainer plate orifice diameter (mm)', '')))
            self.ui.subLnEdtMeshMat.setText(sys_specs.get('Mesh material', ''))
            self.ui.subLnEdtMeshSize.setText(sys_specs.get('Mesh size', ''))

            # # Propellant Specs
            prop_specs = test_data.get('propellant_specs', {})
            self.ui.subLnEdtTypeOfProp.setText(prop_specs.get('Type of Propellant', ''))
            self.ui.subLnEdtConcBefTest.setValue(safe_float(prop_specs.get('Concentration before testing (%)', '')))
            self.ui.subLnEdtStability.setText(prop_specs.get('Stability (Old/New -MIL)', ''))
            self.ui.subLnEdtWghtOfPropBefTest.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_before the test (g)', '')))
            self.ui.subLnEdtWghtOfPropAftTest.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_after the test (g)', '')))

            # # Catalyst Specs
            cat_specs = test_data.get('catalyst_specs', {})
            self.ui.subLnEdtCatType.setText(cat_specs.get('Catalyst_type', ''))
            self.ui.subLnEdtCatGrade.setText(cat_specs.get('Catalyst_Grade/ Composition', ''))
            self.ui.subLnEdtCatSize.setText(cat_specs.get('Catalyst_size (mm)', ''))
            self.ui.subLnEdtCatWghtBefTest.setValue(
                safe_float(cat_specs.get('Weight_of_the_catalyst_before the test (g)', '')))
            # self.ui.subLnEdtPrehtTemp.setText(cat_specs.get('Preheat_temperature (°C)', ''))

            # Component Details
            comp_details = test_data.get('component_details', {})
            print(f'Component Details contains: {comp_details}')
            self.ui.Vac_Chamb_Pressure_Sensr_type_Input.setText(comp_details[0].get('Pressure_sensor_type', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[0].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_range_Input.setText(comp_details[0].get('Pressure_sensor_range', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.setText(comp_details[0].get('Pressure_sensor_input_and_output', ''))
            self.ui.Prop_Tank_Pressure_Sensr_type_Input.setText(comp_details[1].get('Pressure_sensor_type', ''))
            self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[1].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Prop_Tank_Pressure_Snsr_range_Input.setText(comp_details[1].get('Pressure_sensor_range', ''))
            self.ui.Prop_Tank__Pressure_Snsr_IO_Input.setText(comp_details[1].get('Pressure_sensor_input_and_output', ''))
            self.ui.Thruster_Pressure_Sensr_type_Input.setText(comp_details[2].get('Pressure_sensor_type', ''))
            self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[2].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Thruster_Pressure_Snsr_range_Input.setText(comp_details[2].get('Pressure_sensor_range', ''))
            self.ui.Thruster_Pressure_Snsr_IO_Input.setText(comp_details[2].get('Pressure_sensor_input_and_output', ''))
            self.ui.subLnEdtHtrType.setText(comp_details[3].get('Heater_type', ''))
            self.ui.subLnEdtHtrInpPower.setValue(safe_float(comp_details[3].get('Heater_input_power (W)', '')))

            # Test Details
            test_details = test_data.get('test_details', {})
            self.ui.subLnEdtPropTnkHtrCtOfTemp.setValue(
                safe_float(test_details.get('Propellant_tank_heater_cut-off_temperature (°C)', '')))
            self.ui.subLnEdtPropTnkHtrRstTemp.setValue(
                safe_float(test_details.get('Propellant_tank_heater_reset_temperature (°C)', '')))
            self.ui.subLblTestProcValue.setPlainText(test_details.get('Test_procedure', ''))

            # Heater Information
            heater_info = test_data.get('heater_info', {})
            self.ui.subLnEdtHtrType_2.setText(heater_info.get('Heater_type', ''))

            # Handle heater input values - check both old and new formats for compatibility
            # New format (direct fields)
            voltage = heater_info.get('Heater_input_Voltage', '')
            current = heater_info.get('Heater_input_Current', '')
            wattage = heater_info.get('Heater_input_Wattage', '')

            # Old format (nested in Heater_input object) - fallback for compatibility
            if not voltage and not current and not wattage:
                heater_input = heater_info.get('Heater_input', {})
                if isinstance(heater_input, dict):
                    voltage = heater_input.get('Voltage', '')
                    current = heater_input.get('Current', '')
                    wattage = heater_input.get('Wattage', '')

            self.ui.subLnEdtHtrInpVoltage.setValue(safe_float(voltage))
            self.ui.subLnEdtHtrInpCurrent.setValue(safe_float(current))
            self.ui.subLnEdtHtrInpWattage.setValue(safe_float(wattage))
            self.ui.subLnEdtHtrCtOfTemp.setValue(safe_float(heater_info.get('Heater_cut_off_temp (°C)', '')))
            self.ui.subLnEdtHtrRstTemp.setValue(safe_float(heater_info.get('Heater_reset_temp (°C)', '')))

            # Heater Cycles
            heater_cycles = test_data.get('heater_cycles', {})
            for cycle in range(0, 4):
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOnTime').setTime(
                    QTime.fromString(heater_cycles[cycle].get('switch_on', ), 'HH:mm'))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtONCorspgTankPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_on_corresponding_tank_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtONCorspgThrusterPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_on_corresponding_thruster_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOffTime').setTime(
                    QTime.fromString(heater_cycles[cycle].get('switch_off', ), 'HH:mm'))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOFFCorspgTankPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_off_corresponding_tank_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOFFCorspgThrusterPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_off_corresponding_thruster_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtMaxTemp').setValue(
                    safe_float(heater_cycles[cycle].get('max_temp', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtLoc').setText(
                    heater_cycles[cycle].get('max_temp_location', ''))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtCorrespgTemp').setValue(
                    safe_float(heater_cycles[cycle].get('tank_bottom_temp', '')))

            # Post Test Observations
            post_test_obs = test_data.get('post_test_observations', {})
            self.ui.subLnEdtChmbrNoPostTestObs.setText(post_test_obs.get('Chamber_number', ''))
            self.ui.subLnEdtChmbrLen_2.setValue(safe_float(post_test_obs.get('Chamber_length (mm)', '')))
            self.ui.subLnEdtChmbrIntDia_2.setValue(safe_float(post_test_obs.get('Chamber_internal_diameter (mm)', '')))
            self.ui.subLnEdtChmbrExtDia_2.setValue(safe_float(post_test_obs.get('Chamber_external_diameter (mm)', '')))
            self.ui.subLnEdtMeshCond_2.setText(post_test_obs.get('Mesh_condition', ''))
            self.ui.subLnEdtRetainerPltCond_2.setText(post_test_obs.get('Retainer_plate_condition', ''))
            self.ui.subLnEdtNote.setPlainText(test_data.get('note', ''))

            # Catalyst Post Analysis
            cat_post_analysis = test_data.get('catalyst_post_analysis', {})
            self.ui.subLnEdtCatDet.setText(cat_post_analysis.get('catalyst_details/specification', ''))
            self.ui.subLnEdtCatColBfr.setText(cat_post_analysis.get('catalyst_color_before', ''))
            self.ui.subLnEdtCatColAft.setText(cat_post_analysis.get('catalyst_color_after', ''))
            self.ui.subLnEdtCatWghtFild.setValue(safe_float(cat_post_analysis.get('catalyst_weight_filled', '')))
            self.ui.subLnEdtCatWghtRecvrd.setValue(safe_float(cat_post_analysis.get('catalyst_weight_recovered', '')))
            self.ui.subLnEdtCatLosPerc.setValue(safe_float(cat_post_analysis.get('catalyst_change_percentage', '')))

            # Propellant Post Analysis
            prop_post_analysis = test_data.get('propellant_post_analysis', {})
            self.ui.subLnEdtPropDet_2.setText(prop_post_analysis.get('Propellant_details/specification', ''))
            self.ui.subLnEdtPropColBef_2.setText(prop_post_analysis.get('Propellant_color_before', ''))
            self.ui.subLnEdtPropColAft_2.setText(prop_post_analysis.get('Propellant_color_after', ''))
            self.ui.subLnEdtPropWghtFild_2.setValue(safe_float(prop_post_analysis.get('Propellant_weight_filled (g)', '')))
            self.ui.subLnEdtPropWghtRecvrd_2.setValue(
                safe_float(prop_post_analysis.get('Propellant_weight_recovered (g)', '')))
            self.ui.subLnEdtPropUsedPerc_2.setValue(safe_float(prop_post_analysis.get('Propellant_used_percentage (%)', '')))
            self.ui.subLnEdtPropRIBefFirg_2.setValue(safe_float(prop_post_analysis.get('Propellant_RI_(before_firing)', '')))
            self.ui.subLnEdtPropRIAftFirg_2.setValue(safe_float(prop_post_analysis.get('Propellant_RI_(after_firing)', '')))
            self.ui.subLnEdtFirgDur_2.setValue(safe_float(prop_post_analysis.get('Firing_duration (s)', '')))
            self.ui.subLnEdtApproxMassFlowRate_2.setValue(safe_float(prop_post_analysis.get('Approximate_mass_flow_rate (mg/s)', '')))
            self.ui.subLblPropBefRITable.setNum(safe_float(prop_post_analysis.get('prop_ri_bef_table', '')))
            self.ui.subLblPropAftRITable.setNum(safe_float(prop_post_analysis.get('prop_ri_aft_table', '')))
            self.ui.subLblPropBefConcTable.setNum(safe_float(prop_post_analysis.get('prop_conc_bef_table', '')))
            self.ui.subLblPropAftConcTable.setNum(safe_float(prop_post_analysis.get('prop_conc_aft_table', '')))

            # Update performance data if available
            sys_perf_data = test_data.get('performance_data', {})
            self.ui.subLnEdtChambPressure.setText(sys_perf_data.get('Chamber_pressure (mbar)', ''))
            self.ui.subLnEdtVacPressure.setText(sys_perf_data.get('Vacuum_chamber_pressure (mbar)', ''))
            self.ui.subLnEdtChambTemp.setText(sys_perf_data.get('Maximum_temperature (K)', ''))
            self.ui.subLnEdtCharVelo.setText(sys_perf_data.get('Characteristic_velocity (m/s)', ''))
            self.ui.subLnEdtCoefOfThrust.setText(sys_perf_data.get('Coefficient_of_thrust', ''))
            self.ui.subLnEdtBurnTime.setText(sys_perf_data.get('Burn_time (s)', ''))
            self.ui.subLnEdtMassFlowRate.setText(sys_perf_data.get('Mass_flow_rate (mg/s)', ''))
            self.ui.subLnEdtThrust.setText(sys_perf_data.get('Thrust (mN)', ''))
            self.ui.subLnEdtSpcImpulse.setText(sys_perf_data.get('Specific_impulse (s)', ''))
            self.ui.subLnEdtTotImpulse.setText(sys_perf_data.get('Total_impulse (Ns)', ''))
            self.ui.lnEdtInitialPropMass.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_before the test (g)', '')))
            self.ui.lnEdtFinalPropMass.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_after the test (g)', '')))
            self.ui.lnEdtChambPressRangeMin.setValue(
                safe_float(sys_perf_data.get('Chamber_pressure_lower_limit (s)', '')))
            self.ui.lnEdtChambPressRangeMax.setValue(
                safe_float(sys_perf_data.get('Chamber_pressure_upper_limit (s)', '')))
            self.ui.lnEdtVacPressRangeMin.setValue(
                safe_float(sys_perf_data.get('Vacuum_pressure_lower_limit (s)', '')))
            self.ui.lnEdtVacPressRangeMax.setValue(
                safe_float(sys_perf_data.get('Vacuum_pressure_upper_limit (s)', '')))

            # Update test Authorization
            test_authorization = test_data.get('test_authorization', {})
            self.ui.subLnEdtTestConductedBy.setText(test_authorization.get('conducted_by', ''))
            self.ui.subLnEdtReportGeneratedBy.setText(test_authorization.get('generated_by', ''))
            self.ui.subLnEdtReportAuthorizedBy.setText(test_authorization.get('authorized_by', ''))

            # Update pressure relations
            pressure_relations = test_data.get('pressure_relations', {})
            self.ui.lnEdtY0PressureRelation.setText(pressure_relations.get('Vacuum_chamber_pressure_relation', ''))
            self.ui.lnEdtY1PressureRelation.setText(pressure_relations.get('Propellant_tank_pressure_relation', ''))
            self.ui.lnEdtY2PressureRelation.setText(pressure_relations.get('Thruster_chamber_pressure_relation', ''))

            # Force enable the plots button if temperature or pressure data is available
            if 'temperature_data' in test_data or 'pressure_data' in test_data:
                # Force enable the plots button
                self.force_enable_plots_button()

                # Set up multiple timers to keep trying to enable the button
                for delay in [500, 1000, 2000]:
                    QTimer.singleShot(delay, self.force_enable_plots_button)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error updating UI with test data: {str(e)}"
            )
            traceback.print_exc()  # This will print the full error traceback to console

    def handle_plots_mode_change(self, mode_name, mode_index):
        if mode_name == 'Temperature' and self.temperature_data is None:
            QMessageBox.warning(self, 'Load Data', 'Please, load the Temperature Data first!')
            self.mode_plot_settings.set_mode(1)
        elif mode_name == 'Pressure' and self.pressure_data is None:
            QMessageBox.warning(self, 'Load Data', 'Please, load the Pressure Data first!')
            self.mode_plot_settings.set_mode(0)

    def handle_mode_change(self, mode_name, mode_index):
        """Handle theme mode changes."""
        if mode_name == "New Test":
            self.menuBar().clear()
            self.ui.tabWidget.setCurrentIndex(0)
        elif mode_name == "Existing Test":
            self.menuBar().clear()
            self.ui.tabWidget.setCurrentIndex(0)
            self.existing_data_dialog.exec()
        elif mode_name == "Database":
            if self._initialize_authentication():  # Check return value
                self.ui.tabWidget.setCurrentIndex(1)
            else:
                self.ui.tabWidget.setCurrentIndex(0)  # Reset to first mode
        elif mode_name == "Temperature":
            self.update_content(self.ui.plots)
            self.show_plot_settings('temperature')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.temperature_tab)
            self.ui.lblCurentSection.setText('Temperature Plot')
        elif mode_name == "Pressure":
            self.update_content(self.ui.plots)
            self.show_plot_settings('pressure')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.pressure_tab)
            self.ui.lblCurentSection.setText('Pressure Plot')

    def handle_section_change(self, section_name):
        """Handle section button clicks with animations"""
        try:
            # Update current section label in the top bar
            self.ui.lblCurentSection.setText(section_name)

            # Define a mapping of section names to their keys and widgets
            section_mapping = {
                "Test Prerequisite": ('test_prereq', self.ui.testPrereqSubSections, self.ui.basicInformation),
                "Heater Operation": ('heater_op', self.ui.htrOpSubSections, self.ui.heaterInformation),
                "Post Test Analysis": ('post_test', self.ui.pstTestAnSubSections, self.ui.postTestingObs),
                "PlotWindow": ('plot_controls', self.ui.plotControlsFrame, self.ui.plots),
                "Performance": ('performance', self.ui.perforSubSections, self.ui.performance)
            }

            # Get section info and handle the change
            if section_name in section_mapping:
                section_key, section_widget, content_widget = section_mapping[section_name]
                self.ui.contentStack.setCurrentWidget(content_widget)
                self.toggle_section(section_key, section_widget)

                # Get current page name
                current_page = self.ui.contentStack.currentWidget().objectName()

                # Update UI elements
                self.update_button_states(current_page)

                # Update section header
                if current_page in self.section_headers:
                    self.ui.lblCurentSection.setText(self.section_headers[current_page])
                if section_name == 'PlotWindow':
                    self.ui.lblCurentSection.setText('Temperature Plot')
                    if self.pressure_data is not None and self.temperature_data is None:
                        self.mode_plot_settings.set_mode(1)

            elif section_name == "Load_Data":
                self.ui.contentStack.setCurrentWidget(self.ui.plotDataLoad)

        except Exception as e:
            print(f"Error handling section change: {str(e)}")

    def setupAdvancedAnimation(self):
        """Configure advanced animation settings"""
        try:
            for animation in self.section_animations.values():
                # Use custom easing curve for more natural motion
                custom_curve = QEasingCurve(QEasingCurve.OutCubic)
                custom_curve.setAmplitude(1.0)
                custom_curve.setPeriod(0.3)
                animation.animation.setEasingCurve(custom_curve)
                animation.opacity_animation.setEasingCurve(custom_curve)

                # Connect finished signal to handle cleanup
                animation.animation.finished.connect(
                    lambda w=animation.widget: self.animationFinished(w)
                )

                # Ensure initial state
                animation.widget.setMaximumHeight(16777215)

        except Exception as e:
            print(f"Error setting up advanced animation: {str(e)}")

    def animationFinished(self, widget):
        """Handle cleanup after animation finishes"""
        try:
            if widget.maximumHeight() == 0:
                widget.hide()
                # Reset maximum height for next expansion
                widget.setMaximumHeight(16777215)
            else:
                # Ensure widget is fully expanded
                widget.setMaximumHeight(16777215)
        except Exception as e:
            print(f"Error in animation finished handler: {str(e)}")

    def delete_temp_report_folder(self):
        """Clean up temporary directory"""
        try:
            if hasattr(self, 'temp_dir') and self.temp_dir:
                if os.path.exists(self.temp_dir):
                    shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Error cleaning up temp directory: {str(e)}")

    def closeEvent(self, event):
        """
        Handle the window close event (X button, Alt+F4, or close() called)
        """
        try:
            # Stop the auto-save timer
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_auto_save()
                except Exception as e:
                    print(f"Error stopping auto-save timer: {str(e)}")

            # Close all matplotlib figures
            try:
                plt.close('all')
            except Exception as e:
                print(f"Error closing matplotlib figures: {str(e)}")

            # Delete temporary plot folders
            if hasattr(self, 'db_handler') and self.db_handler is not None:
                try:
                    self.db_handler.delete_temp_plots_folder()
                except Exception as e:
                    print(f"Error deleting temp plots folder: {str(e)}")
        except Exception as e:
            print(f"Error during application cleanup: {str(e)}")

        # Call parent class closeEvent
        super().closeEvent(event)

    def __del__(self):
        """Cleanup method to ensure all resources are released when the window is destroyed"""
        try:
            # Close all matplotlib figures
            plt.close('all')

            # Stop the auto-save timer
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_auto_save()
                except (RuntimeError, AttributeError, Exception):
                    # Silently ignore errors when accessing auto_saver
                    pass
        except Exception:
            # Silently ignore all errors in destructor
            pass

    def force_enable_plots_button(self):
        """Enable the plots button only if valid data exists"""
        print("\n==== CHECKING BEFORE ENABLING PLOTS BUTTON ====\n")

        # Check if temperature data is valid
        temp_valid = False
        if hasattr(self, 'temperature_data') and self.temperature_data is not None:
            try:
                if isinstance(self.temperature_data, pd.DataFrame):
                    if not self.temperature_data.empty and len(self.temperature_data) > 1:
                        # Check if it has at least one temperature column (not just time)
                        temp_columns = [col for col in self.temperature_data.columns
                                      if col != 'time' and col != 'Time']
                        if len(temp_columns) > 0:
                            temp_valid = True
                            print(f"Valid temperature data found: {self.temperature_data.shape}")
            except Exception as e:
                print(f"Error checking temperature data: {str(e)}")

        # Check if pressure data is valid
        pressure_valid = False
        if hasattr(self, 'pressure_data') and self.pressure_data is not None:
            try:
                if isinstance(self.pressure_data, pd.DataFrame):
                    if not self.pressure_data.empty and len(self.pressure_data) > 1:
                        # Check if it has at least one pressure column (not just time)
                        pressure_columns = [col for col in self.pressure_data.columns
                                          if col != 'time' and col != 'Time']
                        if len(pressure_columns) > 0:
                            pressure_valid = True
                            print(f"Valid pressure data found: {self.pressure_data.shape}")
            except Exception as e:
                print(f"Error checking pressure data: {str(e)}")

        # Only enable the button if we have valid data
        if temp_valid or pressure_valid:
            try:
                print("Enabling plots button due to valid data")
                self.ui.btnPlots.setEnabled(True)
                print(f"Plots button enabled state: {self.ui.btnPlots.isEnabled()}")
            except Exception as e:
                print(f"Error enabling plots button: {str(e)}")
        else:
            try:
                print("No valid data found, disabling plots button")
                self.ui.btnPlots.setEnabled(False)
                print(f"Plots button disabled state: {self.ui.btnPlots.isEnabled()}")
            except Exception as e:
                print(f"Error disabling plots button: {str(e)}")


def main():
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    app.setWindowIcon(QIcon("assets/icon.ico"))

    # Create and show splash screen
    splash = CustomSplashScreen()
    splash.show()

    # Create main window
    main_window = MainWindow()

    # Create and start init thread
    init_thread = InitThread()
    init_thread.status_update.connect(splash.update_status)

    # Play the boot sound when the splash screen is shown
    def on_init_finished():
        splash.play_boot_sound()
        # Adding a small delay to ensure sound starts playing before window transition
        QTimer.singleShot(500, lambda: (splash.close(), main_window.show()))

    init_thread.finished.connect(on_init_finished)
    init_thread.start()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()











